<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Morewise - Memecoin Trading Bot Platform Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }
        .hero {
            margin-bottom: 60px;
        }
        .hero h1 {
            font-size: 3.5rem;
            font-weight: 900;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .hero p {
            font-size: 1.5rem;
            opacity: 0.9;
            margin-bottom: 40px;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }
        .feature {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .feature h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #4ecdc4;
        }
        .feature p {
            opacity: 0.8;
            line-height: 1.6;
        }
        .demo-button {
            display: inline-block;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            padding: 15px 40px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.2rem;
            transition: transform 0.3s ease;
            margin: 10px;
        }
        .demo-button:hover {
            transform: translateY(-2px);
        }
        .tech-stack {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 40px;
        }
        .tech-stack h3 {
            color: #4ecdc4;
            margin-bottom: 20px;
        }
        .tech-list {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
        }
        .tech-item {
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.9rem;
        }
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: left;
        }
        .instructions h3 {
            color: #4ecdc4;
            text-align: center;
            margin-bottom: 20px;
        }
        .instructions ol {
            line-height: 1.8;
        }
        .instructions code {
            background: rgba(0, 0, 0, 0.3);
            padding: 2px 8px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="hero">
            <h1>🚀 Morewise Platform</h1>
            <p>Complete Memecoin Trading Bot Platform with Modern UI/UX</p>
            <a href="#demo" class="demo-button">🎯 View Demo Components</a>
            <a href="#setup" class="demo-button">⚡ Setup Instructions</a>
        </div>

        <div class="features">
            <div class="feature">
                <h3>🤖 Trading Bot Marketplace</h3>
                <p>Browse and invest in automated trading bots with different risk levels. Features bot performance metrics, investment flows, and real-time tracking.</p>
            </div>
            <div class="feature">
                <h3>💼 Enhanced Portfolio</h3>
                <p>Comprehensive portfolio management with bot investments, crypto holdings, and performance analytics. Track profits and manage your investments.</p>
            </div>
            <div class="feature">
                <h3>💰 Deposit & Withdraw</h3>
                <p>Multi-cryptocurrency support with QR codes, address management, and secure transaction processing. No wallet connections required.</p>
            </div>
            <div class="feature">
                <h3>📊 Transaction History</h3>
                <p>Complete transaction tracking with filtering, search, and export functionality. Monitor all deposits, withdrawals, and bot activities.</p>
            </div>
            <div class="feature">
                <h3>⚙️ User Settings</h3>
                <p>Profile management, security settings, trading preferences, and support center. Complete account customization.</p>
            </div>
            <div class="feature">
                <h3>🎨 Design System</h3>
                <p>Systematic design tokens, high-quality SVG assets, and consistent UI components. Professional glass morphism aesthetic.</p>
            </div>
        </div>

        <div class="tech-stack">
            <h3>🛠️ Technology Stack</h3>
            <div class="tech-list">
                <span class="tech-item">React + TypeScript</span>
                <span class="tech-item">Tailwind CSS</span>
                <span class="tech-item">FontAwesome Icons</span>
                <span class="tech-item">Glass Morphism UI</span>
                <span class="tech-item">Responsive Design</span>
                <span class="tech-item">Component Architecture</span>
                <span class="tech-item">Design System</span>
                <span class="tech-item">Multi-Crypto Support</span>
            </div>
        </div>

        <div id="setup" class="instructions">
            <h3>🚀 Quick Setup Instructions</h3>
            <ol>
                <li><strong>Install Dependencies:</strong>
                    <br><code>bun install</code> (or npm install)
                </li>
                <li><strong>Start Development Server:</strong>
                    <br><code>bun run dev</code> (or npm run dev)
                </li>
                <li><strong>View Demo Components:</strong>
                    <br>Import and use <code>MainApp</code> component from <code>src/components/MainApp.tsx</code>
                </li>
                <li><strong>Key Files Created:</strong>
                    <ul>
                        <li><code>src/components/MainApp.tsx</code> - Complete platform with navigation</li>
                        <li><code>src/pages/BotMarketplace.tsx</code> - Trading bot selection</li>
                        <li><code>src/pages/EnhancedPortfolio.tsx</code> - Portfolio management</li>
                        <li><code>src/pages/DepositWithdraw.tsx</code> - Fund management</li>
                        <li><code>src/pages/TransactionHistory.tsx</code> - Transaction tracking</li>
                        <li><code>src/pages/UserSettings.tsx</code> - Account settings</li>
                        <li><code>src/components/InvestmentModal.tsx</code> - Investment flow</li>
                        <li><code>src/lib/designSystem.ts</code> - Design tokens</li>
                        <li><code>src/lib/cryptoAssets.ts</code> - Crypto icons & assets</li>
                    </ul>
                </li>
                <li><strong>Demo Usage:</strong>
                    <br>Replace your App component with <code>&lt;MainApp /&gt;</code> to see the complete platform
                </li>
            </ol>
        </div>

        <div style="margin-top: 40px; opacity: 0.7; font-size: 0.9rem;">
            <p>✨ Built with modern React patterns, TypeScript safety, and professional UI/UX design</p>
            <p>🎯 Ready for production with proper error handling, responsive design, and accessibility</p>
        </div>
    </div>
</body>
</html>
