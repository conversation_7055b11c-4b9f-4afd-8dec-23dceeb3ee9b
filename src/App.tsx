/**
 * ==============================================
 * MOREWISE - MEMECOIN TRADING BOT PLATFORM
 * ==============================================
 *
 * Complete memecoin trading bot platform with modern UI/UX.
 * Features bot marketplace, portfolio management, multi-crypto support,
 * and traditional exchange model (no wallet connections required).
 */

import { useState, useEffect, useCallback } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from './lib/icons';



// Import new platform components
import { BotMarketplace } from './pages/BotMarketplace';
import { EnhancedPortfolio } from './pages/EnhancedPortfolio';
import { DepositWithdraw } from './pages/DepositWithdraw';
import { TransactionHistory } from './pages/TransactionHistory';
import { UserSettings } from './pages/UserSettings';
import { EnhancedDashboard } from './pages/EnhancedDashboard';
import { BotSetup } from './pages/BotSetup';
import { BotManagement as UserBotManagement } from './pages/BotManagement';
import { InvestmentModal } from './components/InvestmentModal';

// Import legacy components for admin and auth
import { LandingPage } from './pages/LandingPage';
import { Auth } from './pages/Auth';
import { logout } from './lib/auth';
import { User } from './types';
import { AuthProvider } from './lib/AuthContext';

// Import admin components
import { AdminLogin } from './pages/admin/AdminLogin';
import { AdminDashboard } from './pages/admin/AdminDashboard';
import { UserManagement } from './pages/admin/UserManagement';
import { BotManagement } from './pages/admin/BotManagement';
import { WalletManagement } from './pages/admin/WalletManagement';
import { TransactionManagement } from './pages/admin/TransactionManagement';
import { SupportManagement } from './pages/admin/SupportManagement';
import { AdminLayout } from './components/AdminLayout';
import { getAdminSession } from './lib/adminSupabase';

// User balances interface for the new platform
interface UserBalances extends Record<string, number> {
  USDT: number;
  USDC: number;
  BTC: number;
  ETH: number;
  DOGE: number;
  SHIB: number;
  PEPE: number;
}

// Bot investment interface for the new platform
interface BotInvestmentData {
  id: string;
  botName: string;
  riskLevel: 'low' | 'medium' | 'high';
  investedAmount: number;
  currentValue: number;
  totalReturn: number;
  returnPercent: number;
  dailyReturn: number;
  isActive: boolean;
  investmentDate: string;
}

type Page = 'dashboard' | 'marketplace' | 'portfolio' | 'deposit-withdraw' | 'transactions' | 'settings' | 'bot-setup' | 'bot-management';



/**
 * ==============================================
 * MOREWISE MAIN PLATFORM COMPONENT
 * ==============================================
 *
 * Complete memecoin trading bot platform with modern UI/UX.
 * Handles navigation, state management, and all platform features.
 */
function MorewisePlatform() {
  const [currentPage, setCurrentPage] = useState<Page>('dashboard');
  const [showInvestmentModal, setShowInvestmentModal] = useState(false);
  const [selectedBot, setSelectedBot] = useState<any>(null);

  // User balances state (mock data - replace with real API)
  const [userBalances, setUserBalances] = useState<UserBalances>({
    USDT: 5000,
    USDC: 2500,
    BTC: 0.15,
    ETH: 2.5,
    DOGE: 10000,
    SHIB: 50000000,
    PEPE: 1000000,
  });

  // Bot investments state (mock data)
  const [botInvestments, setBotInvestments] = useState<BotInvestmentData[]>([
    {
      id: '1',
      botName: 'Conservative Growth',
      riskLevel: 'low',
      investedAmount: 2000,
      currentValue: 2156.80,
      totalReturn: 156.80,
      returnPercent: 7.84,
      dailyReturn: 12.50,
      isActive: true,
      investmentDate: '2024-03-10',
    },
    {
      id: '2',
      botName: 'Momentum Hunter',
      riskLevel: 'medium',
      investedAmount: 3000,
      currentValue: 3420.15,
      totalReturn: 420.15,
      returnPercent: 14.01,
      dailyReturn: 28.75,
      isActive: true,
      investmentDate: '2024-03-08',
    },
  ]);



  // Handle investment in bots
  const handleInvestClick = (bot: any) => {
    setSelectedBot(bot);
    setShowInvestmentModal(true);
  };

  const handleInvestment = (amount: number, autoReinvest: boolean) => {
    if (userBalances.USDT >= amount) {
      // Deduct from balance
      setUserBalances(prev => ({
        ...prev,
        USDT: prev.USDT - amount
      }));

      // Add new investment
      const newInvestment: BotInvestmentData = {
        id: Date.now().toString(),
        botName: selectedBot.name,
        riskLevel: selectedBot.riskLevel,
        investedAmount: amount,
        currentValue: amount,
        totalReturn: 0,
        returnPercent: 0,
        dailyReturn: 0,
        isActive: true,
        investmentDate: new Date().toISOString().split('T')[0],
      };

      setBotInvestments(prev => [...prev, newInvestment]);
      setShowInvestmentModal(false);
      setSelectedBot(null);
    }
  };

  // Handle deposits
  const handleDeposit = (currency: string, amount: number) => {
    setUserBalances(prev => ({
      ...prev,
      [currency]: prev[currency] + amount
    }));
  };

  // Handle withdrawals
  const handleWithdraw = (currency: string, amount: number) => {
    if (userBalances[currency] >= amount) {
      setUserBalances(prev => ({
        ...prev,
        [currency]: prev[currency] - amount
      }));
      return true;
    }
    return false;
  };

  // Navigation items for the platform
  const navigationItems = [
    { id: 'dashboard', label: 'Dashboard', icon: ICON_NAMES.HOME },
    { id: 'marketplace', label: 'Marketplace', icon: ICON_NAMES.ROBOT },
    { id: 'portfolio', label: 'Portfolio', icon: ICON_NAMES.WALLET },
    { id: 'deposit-withdraw', label: 'Funds', icon: ICON_NAMES.EXCHANGE },
    { id: 'transactions', label: 'History', icon: ICON_NAMES.CLOCK },
    { id: 'settings', label: 'Settings', icon: ICON_NAMES.SETTINGS },
  ];

  // Render current page content
  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return (
          <EnhancedDashboard
            userBalances={userBalances}
            botInvestments={botInvestments}
            onNavigate={(page: string) => setCurrentPage(page as Page)}
          />
        );
      case 'marketplace':
        return (
          <BotMarketplace
            onInvestClick={handleInvestClick}
            onSetupCustomBot={() => setCurrentPage('bot-setup')}
          />
        );
      case 'portfolio':
        return (
          <EnhancedPortfolio
            onDepositClick={() => setCurrentPage('deposit-withdraw')}
            onWithdrawClick={() => setCurrentPage('deposit-withdraw')}
            onBotClick={(botId: string) => setCurrentPage('marketplace')}
          />
        );
      case 'deposit-withdraw':
        return (
          <DepositWithdraw
            userBalances={userBalances}
            onDeposit={handleDeposit}
            onWithdraw={handleWithdraw}
          />
        );
      case 'transactions':
        return <TransactionHistory />;
      case 'settings':
        return <UserSettings />;
      case 'bot-setup':
        return (
          <BotSetup
            onComplete={(botConfig) => {
              console.log('Bot setup completed:', botConfig);
              setCurrentPage('bot-management');
            }}
            onCancel={() => setCurrentPage('marketplace')}
          />
        );
      case 'bot-management':
        return (
          <UserBotManagement
            onBackToMarketplace={() => setCurrentPage('marketplace')}
          />
        );
      default:
        return (
          <div className="p-4 md:p-6 pb-32 md:pb-6">
            <div className="text-center">
              <h1 className="text-3xl font-black text-gray-900 mb-2">Welcome to Morewise</h1>
              <p className="text-gray-600">Your memecoin trading bot platform</p>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Desktop Sidebar Navigation */}
      <nav className="hidden md:flex fixed left-0 top-0 h-full w-64 bg-white backdrop-blur-lg border-r border-gray-200 z-40 flex-col shadow-xl">
        <div className="p-6">
          <div className="flex items-center space-x-3 mb-8">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
              <FontAwesomeIcon icon={ICON_NAMES.ROBOT} className="text-white text-lg" />
            </div>
            <div className="flex-1">
              <h1 className="text-xl font-black text-gray-900">Morewise</h1>
              <p className="text-xs text-gray-500">Trading Platform</p>
            </div>
          </div>

          <div className="space-y-2">
            {navigationItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setCurrentPage(item.id as Page)}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl font-medium transition-all ${
                  currentPage === item.id
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                }`}
              >
                <FontAwesomeIcon icon={item.icon} className="text-lg" />
                <span>{item.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* User Info */}
        <div className="mt-auto p-6 border-t border-gray-100">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
              <FontAwesomeIcon icon={ICON_NAMES.USER} className="text-white" />
            </div>
            <div>
              <p className="font-semibold text-gray-900">Demo User</p>
              <p className="text-sm text-gray-600">Premium Account</p>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto md:ml-64">
        {renderCurrentPage()}
      </main>

      {/* Bottom Navigation */}
      <nav className="fixed bottom-0 left-0 right-0 bg-white backdrop-blur-lg border-t border-gray-200 md:hidden z-50 shadow-lg">
        <div className="grid grid-cols-6 gap-1 p-2">
          {navigationItems.map((item) => (
            <button
              key={item.id}
              onClick={() => setCurrentPage(item.id as Page)}
              className={`flex flex-col items-center justify-center py-2 px-1 rounded-xl transition-all ${
                currentPage === item.id
                  ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                  : 'text-gray-500 hover:bg-gray-100'
              }`}
            >
              <FontAwesomeIcon icon={item.icon} className="text-lg mb-1" />
              <span className="text-xs font-medium">{item.label}</span>
            </button>
          ))}
        </div>
      </nav>

      {/* Investment Modal */}
      {showInvestmentModal && selectedBot && (
        <InvestmentModal
          isOpen={showInvestmentModal}
          bot={selectedBot}
          userBalance={userBalances.USDT}
          onConfirm={handleInvestment}
          onClose={() => {
            setShowInvestmentModal(false);
            setSelectedBot(null);
          }}
        />
      )}

      {/* Desktop Content Offset - handled by CSS classes */}
    </div>
  );
}

/**
 * Main application content component
 * Routes to the new Morewise platform or legacy auth pages
 */
function AppContent() {
  const navigate = useNavigate();
  const [isLoggedIn, setIsLoggedIn] = useState(true); // Default to logged in for demo
  const [user, setUser] = useState<User | null>({
    id: 'demo-user',
    email: '<EMAIL>',
    username: 'demo',
    name: 'Demo User',
    is_verified: true,
    kyc_status: 'approved',
    referral_code: 'DEMO123',
    preferences: {
      risk_tolerance: 'medium',
      notifications: {
        email: true,
        push: true,
        trade_alerts: true,
        profit_alerts: true,
        loss_alerts: true
      },
      theme: 'light',
      currency: 'USD'
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  } as User);

  // For demo purposes, we'll skip authentication and go straight to the platform
  const checkUserSession = useCallback(async () => {
    // In a real app, this would check Supabase session
    setIsLoggedIn(true);
  }, []);

  useEffect(() => {
    checkUserSession();
  }, [checkUserSession]);


  // For demo purposes, always show the platform
  if (isLoggedIn && user) {
    return <MorewisePlatform />;
  }

  return (
    <Routes>
      <Route path="/" element={<LandingPage />} />
      <Route path="/login" element={<Auth onAuthSuccess={checkUserSession} />} />
      <Route path="/signup" element={<Auth onAuthSuccess={checkUserSession} />} />
      <Route path="*" element={<Navigate to="/login" />} />
    </Routes>
  );
}



// Admin Content Component
function AdminContent() {
  const navigate = useNavigate();
  const location = useLocation();
  const [isAdminLoggedIn, setIsAdminLoggedIn] = useState(false);
  const [adminUser, setAdminUser] = useState<any>(null);
  const [initialLoading, setInitialLoading] = useState(true);
  const [currentAdminPage, setCurrentAdminPage] = useState('dashboard');

  const checkAdminSession = useCallback(async (isInitialLoad = false) => {
    if (isInitialLoad) setInitialLoading(true);
    const { session, adminUser, error } = await getAdminSession();
    if (session && adminUser) {
      setAdminUser(adminUser);
      setIsAdminLoggedIn(true);
    } else {
      setIsAdminLoggedIn(false);
      setAdminUser(null);
    }
    if (isInitialLoad) setInitialLoading(false);
  }, []);

  useEffect(() => {
    checkAdminSession(true);
  }, [checkAdminSession]);

  const handleAdminLogout = useCallback(async () => {
    await logout();
    setAdminUser(null);
    setIsAdminLoggedIn(false);
    navigate('/admin/login');
  }, [navigate]);

  if (initialLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  const renderAdminPage = () => {
    const path = location.pathname;

    if (path === '/admin/login' || !isAdminLoggedIn) {
      return <AdminLogin onLoginSuccess={checkAdminSession} />;
    }

    if (path === '/admin/users') {
      return (
        <AdminLayout currentPage="users" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <UserManagement />
        </AdminLayout>
      );
    }

    if (path === '/admin/bots') {
      return (
        <AdminLayout currentPage="bots" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <BotManagement />
        </AdminLayout>
      );
    }

    if (path === '/admin/wallets') {
      return (
        <AdminLayout currentPage="wallets" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <WalletManagement />
        </AdminLayout>
      );
    }

    if (path === '/admin/transactions') {
      return (
        <AdminLayout currentPage="transactions" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <TransactionManagement />
        </AdminLayout>
      );
    }

    if (path === '/admin/support') {
      return (
        <AdminLayout currentPage="support" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <SupportManagement />
        </AdminLayout>
      );
    }

    if (path === '/admin/analytics') {
      return (
        <AdminLayout currentPage="analytics" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-white mb-4">Analytics Dashboard</h2>
            <p className="text-gray-400">Advanced analytics coming soon...</p>
          </div>
        </AdminLayout>
      );
    }

    if (path === '/admin/logs') {
      return (
        <AdminLayout currentPage="logs" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-white mb-4">Audit Logs</h2>
            <p className="text-gray-400">Audit logging coming soon...</p>
          </div>
        </AdminLayout>
      );
    }

    if (path === '/admin/settings') {
      return (
        <AdminLayout currentPage="settings" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-white mb-4">System Settings</h2>
            <p className="text-gray-400">System configuration coming soon...</p>
          </div>
        </AdminLayout>
      );
    }

    // Default to dashboard
    return (
      <AdminLayout currentPage="dashboard" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
        <AdminDashboard />
      </AdminLayout>
    );
  };

  return renderAdminPage();
}

function App() {
  const location = useLocation();
  const isAdminRoute = location.pathname.startsWith('/admin');

  return (
    <div>
      {isAdminRoute ? <AdminContent /> : <AppContent />}
    </div>
  );
}

function AppWrapper() {
  return (
    <Router>
      <AuthProvider>
        <App />
      </AuthProvider>
    </Router>
  );
}

export default AppWrapper;