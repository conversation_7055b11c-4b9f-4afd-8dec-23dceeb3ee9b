/**
 * ==============================================
 * MAIN APPLICATION COMPONENT
 * ==============================================
 *
 * This is the root component for the crypto meme coin bot trading platform.
 * It handles routing, authentication, and global state management.
 */

import { useState, useEffect, useCallback } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate, Outlet, useLocation } from 'react-router-dom';
import { Header } from './components/Header';
import { Navigation } from './components/Navigation';
import { LandingPage } from './pages/LandingPage';
import { Dashboard } from './pages/Dashboard';
import { Bots } from './pages/Bots';
import { BotDetail } from './pages/BotDetail';
import { Portfolio } from './pages/Portfolio';
import { Wallet } from './pages/Wallet';
import { Settings } from './pages/Settings';
import { Auth } from './pages/Auth';
import { getSession, logout } from './lib/auth';
import { User, Portfolio as PortfolioType, TradingBot, BotInvestment } from './types';
import { supabase } from './lib/supabase';
import { AuthProvider } from './lib/AuthContext';

// Import admin components
import { AdminLogin } from './pages/admin/AdminLogin';
import { AdminDashboard } from './pages/admin/AdminDashboard';
import { UserManagement } from './pages/admin/UserManagement';
import { BotManagement } from './pages/admin/BotManagement';
import { WalletManagement } from './pages/admin/WalletManagement';
import { TransactionManagement } from './pages/admin/TransactionManagement';
import { SupportManagement } from './pages/admin/SupportManagement';
import { AdminLayout } from './components/AdminLayout';
import { getAdminSession } from './lib/adminSupabase';

// Utility function to generate referral codes
function generateReferralCode(): string {
  return Math.random().toString(36).substring(2, 8).toUpperCase();
}

/**
 * Layout component for authenticated routes
 * Provides the main application shell with header, navigation, and content area
 */
function AppLayout({ user, onLogout, onTabChange }: { user: User; onLogout: () => void; onTabChange: (tab: string) => void }) {
  const location = useLocation();
  const activeTab = location.pathname.substring(1) || 'dashboard';

  return (
    <div className="min-h-screen bg-gray-100">
      <Header user={user} onLogout={onLogout} />
      <div className="flex">
        <Navigation
          activeTab={activeTab}
          onTabChange={onTabChange}
          onLogout={onLogout}
        />
        <main className="flex-1 p-6 md:ml-80 pt-6 md:pt-6 pb-20 md:pb-6">
          <Outlet />
        </main>
      </div>
    </div>
  );
}

/**
 * Main application content component
 * Handles user authentication and crypto trading platform state
 */
function AppContent() {
  const navigate = useNavigate();
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [initialLoading, setInitialLoading] = useState(true);
  const [portfolio, setPortfolio] = useState<PortfolioType | null>(null);
  const [tradingBots, setTradingBots] = useState<TradingBot[]>([]);
  const [botInvestments, setBotInvestments] = useState<BotInvestment[]>([]);

  const handleLogout = useCallback(async (shouldRedirect = true) => {
    await logout();
    setUser(null);
    setIsLoggedIn(false);
    setPortfolio(null);
    setBotInvestments([]);
    if (shouldRedirect) {
      navigate('/login');
    }
  }, [navigate]);

  /**
   * Load crypto trading data for authenticated user
   * Includes portfolio, trading bots, and bot investments
   */
  const loadDataForUser = useCallback(async (userId: string) => {
    try {
      // Load trading bots from database
      const { data: bots, error: botsError } = await supabase
        .from('trading_bots')
        .select('*')
        .eq('is_active', true);

      if (botsError) {
        console.error('Error loading bots:', botsError);
      } else {
        setTradingBots(bots || []);
      }

      // Load or create user portfolio
      let { data: portfolioData, error: portfolioError } = await supabase
        .from('portfolios')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (portfolioError || !portfolioData) {
        // Create portfolio if it doesn't exist
        const { data: newPortfolio, error: createError } = await supabase
          .from('portfolios')
          .insert({
            user_id: userId,
            total_balance: 0,
            available_balance: 0,
            invested_amount: 0,
            total_profit_loss: 0,
            currency: 'USD'
          })
          .select()
          .single();

        if (createError) {
          console.error('Error creating portfolio:', createError);
        } else {
          portfolioData = newPortfolio;
        }
      }

      setPortfolio(portfolioData);

      // Load user's bot investments
      const { data: investments, error: investmentsError } = await supabase
        .from('bot_investments')
        .select(`
          *,
          trading_bots (*)
        `)
        .eq('user_id', userId);

      if (investmentsError) {
        console.error('Error loading investments:', investmentsError);
      } else {
        setBotInvestments(investments || []);
      }

      console.log('Real trading data loaded successfully');
    } catch (error) {
      console.error("Error loading trading data:", error);
      await handleLogout();
    }
  }, [handleLogout]);

  /**
   * Assign default wallet bundle to new user
   */
  const assignDefaultWalletBundle = async (userId: string) => {
    try {
      // Check if user already has a wallet assignment
      const { data: existingAssignment } = await supabase
        .from('user_wallet_assignments')
        .select('id')
        .eq('user_id', userId)
        .single();

      if (existingAssignment) {
        return; // User already has a wallet bundle
      }

      // Get the default wallet bundle (first active bundle)
      const { data: defaultBundle } = await supabase
        .from('wallet_bundles')
        .select('id')
        .eq('is_active', true)
        .order('created_at')
        .limit(1)
        .single();

      if (defaultBundle) {
        // Assign the default bundle to the user
        await supabase
          .from('user_wallet_assignments')
          .insert({
            user_id: userId,
            bundle_id: defaultBundle.id,
            is_active: true
          });

        console.log('Default wallet bundle assigned to user');
      }
    } catch (error) {
      console.error('Error assigning wallet bundle:', error);
    }
  };

  /**
   * Check and validate user session
   * Supports both email and wallet-based authentication
   */
  const checkUserSession = useCallback(async (isInitialLoad = false) => {
    if (isInitialLoad) setInitialLoading(true);

    try {
      // Get current Supabase session
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error || !session) {
        await handleLogout(false); // Don't redirect during session check
        if (isInitialLoad) setInitialLoading(false);
        return;
      }

      // Get real user data from Supabase
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', session.user.id)
        .single();

      if (userError || !userData) {
        // Create user record if it doesn't exist
        const { data: newUser, error: createError } = await supabase
          .from('users')
          .insert({
            id: session.user.id,
            email: session.user.email,
            username: session.user.email?.split('@')[0] || 'user',
            name: session.user.user_metadata?.full_name || session.user.email?.split('@')[0] || 'User',
            is_verified: false,
            kyc_status: 'pending',
            referral_code: generateReferralCode(),
            preferences: {
              risk_tolerance: 'medium',
              notifications: { email: true, push: true },
              theme: 'dark',
              currency: 'USD'
            }
          })
          .select()
          .single();

        if (createError) {
          console.error('Error creating user:', createError);
          await handleLogout(false); // Don't redirect during session check
          if (isInitialLoad) setInitialLoading(false);
          return;
        }

        setUser(newUser);
        setIsLoggedIn(true);

        // Assign default wallet bundle to new user
        await assignDefaultWalletBundle(newUser.id);

        await loadDataForUser(newUser.id);
      } else {
        setUser(userData);
        setIsLoggedIn(true);
        await loadDataForUser(userData.id);
      }
    } catch (error) {
      console.error('Session check error:', error);
      await handleLogout(false); // Don't redirect during session check
    }

    if (isInitialLoad) setInitialLoading(false);
  }, [loadDataForUser, handleLogout]);

  useEffect(() => {
    checkUserSession(true); // Only show loading on initial load
  }, [checkUserSession]);

  const handleTabChange = useCallback((tab: string) => {
    navigate(tab === 'dashboard' ? '/' : `/${tab}`);
  }, [navigate]);

  /**
   * Handle bot investment with real Supabase data
   */
  const handleBotInvestment = async (botId: string, amount: number) => {
    if (!user) return { success: false, error: 'User not authenticated' };
    try {
      // Check if user has sufficient balance
      if (!portfolio || portfolio.available_balance < amount) {
        return { success: false, error: 'Insufficient balance' };
      }

      // Create bot investment record
      const { data: investment, error: investmentError } = await supabase
        .from('bot_investments')
        .insert({
          user_id: user.id,
          bot_id: botId,
          amount: amount,
          status: 'active',
          profit_loss: 0,
          start_date: new Date().toISOString()
        })
        .select()
        .single();

      if (investmentError) {
        console.error('Investment error:', investmentError);
        return { success: false, error: 'Failed to create investment' };
      }

      // Update portfolio balances
      const { error: portfolioError } = await supabase
        .from('portfolios')
        .update({
          available_balance: portfolio.available_balance - amount,
          invested_amount: portfolio.invested_amount + amount
        })
        .eq('user_id', user.id);

      if (portfolioError) {
        console.error('Portfolio update error:', portfolioError);
        return { success: false, error: 'Failed to update portfolio' };
      }

      // Reload user data to reflect changes
      await loadDataForUser(user.id);
      return { success: true };
    } catch (error) {
      console.error('Investment error:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Investment failed' };
    }
  };

  /**
   * Handle user profile updates
   */
  const handleUserUpdate = async (updatedUser: Partial<User>) => {
    if (!user) return { error: 'No user logged in' };
    try {
      const { data, error } = await supabase.from('users').update(updatedUser).eq('id', user.id).select().single();
      if (error) throw error;
      if (data) setUser(data);
      return { success: true, user: data };
    } catch (error) {
      console.error('Error updating user:', error);
      return { success: false, error };
    }
  };

  /**
   * Handle bot detail navigation
   */
  const handleBotClick = useCallback((botId: string) => {
    navigate('/bots/detail', { state: { botId } });
  }, [navigate]);


  return (
    <Routes>
      <Route path="/" element={isLoggedIn ? <Navigate to="/dashboard" /> : <LandingPage />} />
      <Route path="/login" element={isLoggedIn ? <Navigate to="/dashboard" /> : <Auth onAuthSuccess={checkUserSession} />} />
      <Route path="/signup" element={isLoggedIn ? <Navigate to="/dashboard" /> : <Auth onAuthSuccess={checkUserSession} />} />
      {isLoggedIn && user ? (
        <Route path="/*" element={<AppLayout user={user} onLogout={handleLogout} onTabChange={handleTabChange} />}>
          <Route
            path="dashboard"
            element={
              <Dashboard
                portfolio={portfolio}
                tradingBots={tradingBots}
                botInvestments={botInvestments}
                user={user}
                onBotClick={handleBotClick}
                onTabChange={handleTabChange}
              />
            }
          />
          <Route path="bots" element={<Bots onBotClick={handleBotClick} tradingBots={tradingBots} />} />
          <Route path="bots/detail" element={<BotDetail onInvest={handleBotInvestment} tradingBots={tradingBots} />} />
          <Route
            path="portfolio"
            element={
              <Portfolio
                portfolio={portfolio}
                botInvestments={botInvestments}
                user={user}
                onBotClick={handleBotClick}
              />
            }
          />
          <Route
            path="wallet"
            element={
              <Wallet
                walletBalance={portfolio?.wallet_balance || { usd: 0, eth: 0, btc: 0, sol: 0, usdc: 0 }}
                user={user}
              />
            }
          />
          <Route path="settings" element={<Settings user={user} onUserUpdate={handleUserUpdate} />} />
        </Route>
      ) : (
        <Route path="*" element={<Navigate to="/" replace />} />
      )}
    </Routes>
  );
}



// Admin Content Component
function AdminContent() {
  const navigate = useNavigate();
  const location = useLocation();
  const [isAdminLoggedIn, setIsAdminLoggedIn] = useState(false);
  const [adminUser, setAdminUser] = useState<any>(null);
  const [initialLoading, setInitialLoading] = useState(true);
  const [currentAdminPage, setCurrentAdminPage] = useState('dashboard');

  const checkAdminSession = useCallback(async (isInitialLoad = false) => {
    if (isInitialLoad) setInitialLoading(true);
    const { session, adminUser, error } = await getAdminSession();
    if (session && adminUser) {
      setAdminUser(adminUser);
      setIsAdminLoggedIn(true);
    } else {
      setIsAdminLoggedIn(false);
      setAdminUser(null);
    }
    if (isInitialLoad) setInitialLoading(false);
  }, []);

  useEffect(() => {
    checkAdminSession(true);
  }, [checkAdminSession]);

  const handleAdminLogout = useCallback(async () => {
    await logout();
    setAdminUser(null);
    setIsAdminLoggedIn(false);
    navigate('/admin/login');
  }, [navigate]);

  if (initialLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  const renderAdminPage = () => {
    const path = location.pathname;

    if (path === '/admin/login' || !isAdminLoggedIn) {
      return <AdminLogin onLoginSuccess={checkAdminSession} />;
    }

    if (path === '/admin/users') {
      return (
        <AdminLayout currentPage="users" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <UserManagement />
        </AdminLayout>
      );
    }

    if (path === '/admin/bots') {
      return (
        <AdminLayout currentPage="bots" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <BotManagement />
        </AdminLayout>
      );
    }

    if (path === '/admin/wallets') {
      return (
        <AdminLayout currentPage="wallets" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <WalletManagement />
        </AdminLayout>
      );
    }

    if (path === '/admin/transactions') {
      return (
        <AdminLayout currentPage="transactions" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <TransactionManagement />
        </AdminLayout>
      );
    }

    if (path === '/admin/support') {
      return (
        <AdminLayout currentPage="support" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <SupportManagement />
        </AdminLayout>
      );
    }

    if (path === '/admin/analytics') {
      return (
        <AdminLayout currentPage="analytics" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-white mb-4">Analytics Dashboard</h2>
            <p className="text-gray-400">Advanced analytics coming soon...</p>
          </div>
        </AdminLayout>
      );
    }

    if (path === '/admin/logs') {
      return (
        <AdminLayout currentPage="logs" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-white mb-4">Audit Logs</h2>
            <p className="text-gray-400">Audit logging coming soon...</p>
          </div>
        </AdminLayout>
      );
    }

    if (path === '/admin/settings') {
      return (
        <AdminLayout currentPage="settings" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-white mb-4">System Settings</h2>
            <p className="text-gray-400">System configuration coming soon...</p>
          </div>
        </AdminLayout>
      );
    }

    // Default to dashboard
    return (
      <AdminLayout currentPage="dashboard" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
        <AdminDashboard />
      </AdminLayout>
    );
  };

  return renderAdminPage();
}

function App() {
  const location = useLocation();
  const isAdminRoute = location.pathname.startsWith('/admin');

  return (
    <div>
      {isAdminRoute ? <AdminContent /> : <AppContent />}
    </div>
  );
}

function AppWrapper() {
  return (
    <Router>
      <AuthProvider>
        <App />
      </AuthProvider>
    </Router>
  );
}

export default AppWrapper;