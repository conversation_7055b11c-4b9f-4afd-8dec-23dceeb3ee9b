/**
 * ==============================================
 * THEME CONTEXT AND PROVIDER
 * ==============================================
 * 
 * Comprehensive theme management with dark/light mode support,
 * persistent storage, and system preference detection.
 */

import React, { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'light' | 'dark' | 'system';

interface ThemeContextType {
  theme: Theme;
  actualTheme: 'light' | 'dark';
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setThemeState] = useState<Theme>(() => {
    // Get theme from localStorage or default to system
    const savedTheme = localStorage.getItem('morewise-theme') as Theme;
    return savedTheme || 'system';
  });

  const [actualTheme, setActualTheme] = useState<'light' | 'dark'>('light');

  // Function to get system preference
  const getSystemTheme = (): 'light' | 'dark' => {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  };

  // Function to determine actual theme based on preference
  const determineActualTheme = (themePreference: Theme): 'light' | 'dark' => {
    if (themePreference === 'system') {
      return getSystemTheme();
    }
    return themePreference;
  };

  // Update actual theme when theme preference changes
  useEffect(() => {
    const newActualTheme = determineActualTheme(theme);
    setActualTheme(newActualTheme);

    // Apply theme to document
    const root = document.documentElement;
    if (newActualTheme === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }

    // Save to localStorage
    localStorage.setItem('morewise-theme', theme);
  }, [theme]);

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = () => {
      if (theme === 'system') {
        const newActualTheme = getSystemTheme();
        setActualTheme(newActualTheme);
        
        const root = document.documentElement;
        if (newActualTheme === 'dark') {
          root.classList.add('dark');
        } else {
          root.classList.remove('dark');
        }
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [theme]);

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
  };

  const toggleTheme = () => {
    if (theme === 'light') {
      setTheme('dark');
    } else if (theme === 'dark') {
      setTheme('system');
    } else {
      setTheme('light');
    }
  };

  const value: ThemeContextType = {
    theme,
    actualTheme,
    setTheme,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

// Theme-aware color utilities
export const themeColors = {
  light: {
    // Background colors
    bg: {
      primary: 'bg-white',
      secondary: 'bg-gray-50',
      tertiary: 'bg-gray-100',
      card: 'bg-white',
      overlay: 'bg-black bg-opacity-50',
    },
    // Text colors
    text: {
      primary: 'text-gray-900',
      secondary: 'text-gray-600',
      tertiary: 'text-gray-500',
      inverse: 'text-white',
    },
    // Border colors
    border: {
      primary: 'border-gray-200',
      secondary: 'border-gray-300',
      focus: 'border-purple-500',
    },
    // Interactive states
    hover: {
      bg: 'hover:bg-gray-50',
      text: 'hover:text-gray-700',
    },
    // Status colors
    status: {
      success: 'text-green-600 bg-green-100',
      warning: 'text-yellow-600 bg-yellow-100',
      error: 'text-red-600 bg-red-100',
      info: 'text-blue-600 bg-blue-100',
    },
  },
  dark: {
    // Background colors
    bg: {
      primary: 'dark:bg-gray-900',
      secondary: 'dark:bg-gray-800',
      tertiary: 'dark:bg-gray-700',
      card: 'dark:bg-gray-800',
      overlay: 'dark:bg-black dark:bg-opacity-70',
    },
    // Text colors
    text: {
      primary: 'dark:text-white',
      secondary: 'dark:text-gray-300',
      tertiary: 'dark:text-gray-400',
      inverse: 'dark:text-gray-900',
    },
    // Border colors
    border: {
      primary: 'dark:border-gray-700',
      secondary: 'dark:border-gray-600',
      focus: 'dark:border-purple-400',
    },
    // Interactive states
    hover: {
      bg: 'dark:hover:bg-gray-700',
      text: 'dark:hover:text-gray-200',
    },
    // Status colors
    status: {
      success: 'dark:text-green-400 dark:bg-green-900',
      warning: 'dark:text-yellow-400 dark:bg-yellow-900',
      error: 'dark:text-red-400 dark:bg-red-900',
      info: 'dark:text-blue-400 dark:bg-blue-900',
    },
  },
};

// Helper function to get theme-aware classes
export const getThemeClasses = (lightClass: string, darkClass: string) => {
  return `${lightClass} ${darkClass}`;
};

// Comprehensive theme class combinations
export const themeClass = {
  // Backgrounds
  bgPrimary: getThemeClasses(themeColors.light.bg.primary, themeColors.dark.bg.primary),
  bgSecondary: getThemeClasses(themeColors.light.bg.secondary, themeColors.dark.bg.secondary),
  bgTertiary: getThemeClasses(themeColors.light.bg.tertiary, themeColors.dark.bg.tertiary),
  bgCard: getThemeClasses(themeColors.light.bg.card, themeColors.dark.bg.card),
  bgOverlay: getThemeClasses(themeColors.light.bg.overlay, themeColors.dark.bg.overlay),

  // Text
  textPrimary: getThemeClasses(themeColors.light.text.primary, themeColors.dark.text.primary),
  textSecondary: getThemeClasses(themeColors.light.text.secondary, themeColors.dark.text.secondary),
  textTertiary: getThemeClasses(themeColors.light.text.tertiary, themeColors.dark.text.tertiary),
  textInverse: getThemeClasses(themeColors.light.text.inverse, themeColors.dark.text.inverse),

  // Borders
  borderPrimary: getThemeClasses(themeColors.light.border.primary, themeColors.dark.border.primary),
  borderSecondary: getThemeClasses(themeColors.light.border.secondary, themeColors.dark.border.secondary),
  borderFocus: getThemeClasses(themeColors.light.border.focus, themeColors.dark.border.focus),

  // Interactive states
  hoverBg: getThemeClasses(themeColors.light.hover.bg, themeColors.dark.hover.bg),
  hoverText: getThemeClasses(themeColors.light.hover.text, themeColors.dark.hover.text),

  // Status
  statusSuccess: getThemeClasses(themeColors.light.status.success, themeColors.dark.status.success),
  statusWarning: getThemeClasses(themeColors.light.status.warning, themeColors.dark.status.warning),
  statusError: getThemeClasses(themeColors.light.status.error, themeColors.dark.status.error),
  statusInfo: getThemeClasses(themeColors.light.status.info, themeColors.dark.status.info),
};

// Theme-aware component classes
export const themeComponents = {
  card: `${themeClass.bgCard} ${themeClass.borderPrimary} border rounded-2xl shadow-lg`,
  input: `${themeClass.bgPrimary} ${themeClass.textPrimary} ${themeClass.borderSecondary} border rounded-xl px-4 py-3 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors`,
  button: {
    primary: 'bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-105',
    secondary: `${themeClass.bgSecondary} ${themeClass.textPrimary} ${themeClass.borderSecondary} border rounded-xl ${themeClass.hoverBg} transition-colors`,
    ghost: `${themeClass.textSecondary} ${themeClass.hoverBg} ${themeClass.hoverText} rounded-xl transition-colors`,
  },
  modal: `${themeClass.bgPrimary} rounded-2xl shadow-2xl`,
  dropdown: `${themeClass.bgCard} ${themeClass.borderPrimary} border rounded-xl shadow-lg`,
  sidebar: `${themeClass.bgCard} ${themeClass.borderPrimary} border-r`,
  navbar: `${themeClass.bgCard} ${themeClass.borderPrimary} border-t`,
};
