/**
 * ==============================================
 * UTILITY FUNCTIONS
 * ==============================================
 * 
 * This file contains utility functions for the crypto trading platform.
 * Includes formatting, calculations, and helper functions.
 */

import { TradingBot, BotPerformance, PriceData } from '../types';

// ==============================================
// FORMATTING FUNCTIONS
// ==============================================

/**
 * Format currency values with proper symbols and decimals
 */
export function formatCurrency(amount: number, currency: string = 'USD'): string {
  const formatters = {
    USD: new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }),
    BTC: new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 6,
      maximumFractionDigits: 8,
    }),
    ETH: new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 4,
      maximumFractionDigits: 6,
    }),
  };

  if (currency === 'BTC') {
    return `₿${formatters.BTC.format(amount)}`;
  }
  
  if (currency === 'ETH') {
    return `Ξ${formatters.ETH.format(amount)}`;
  }

  return formatters.USD.format(amount);
}

/**
 * Format percentage values with proper sign and color coding
 */
export function formatPercentage(value: number, includeSign: boolean = true): string {
  const sign = includeSign && value > 0 ? '+' : '';
  return `${sign}${value.toFixed(2)}%`;
}

/**
 * Format large numbers with K, M, B suffixes
 */
export function formatLargeNumber(num: number): string {
  if (num >= 1e9) {
    return (num / 1e9).toFixed(1) + 'B';
  }
  if (num >= 1e6) {
    return (num / 1e6).toFixed(1) + 'M';
  }
  if (num >= 1e3) {
    return (num / 1e3).toFixed(1) + 'K';
  }
  return num.toString();
}

/**
 * Format wallet addresses with ellipsis
 */
export function formatWalletAddress(address: string, startChars: number = 6, endChars: number = 4): string {
  if (!address || address.length <= startChars + endChars) {
    return address;
  }
  return `${address.slice(0, startChars)}...${address.slice(-endChars)}`;
}

/**
 * Format time duration in human readable format
 */
export function formatDuration(hours: number): string {
  if (hours < 1) {
    return `${Math.round(hours * 60)}m`;
  }
  if (hours < 24) {
    return `${hours.toFixed(1)}h`;
  }
  const days = Math.floor(hours / 24);
  const remainingHours = hours % 24;
  if (remainingHours === 0) {
    return `${days}d`;
  }
  return `${days}d ${remainingHours.toFixed(1)}h`;
}

// ==============================================
// CALCULATION FUNCTIONS
// ==============================================

/**
 * Calculate profit/loss percentage
 */
export function calculateProfitLossPercentage(invested: number, current: number): number {
  if (invested === 0) return 0;
  return ((current - invested) / invested) * 100;
}

/**
 * Calculate compound annual growth rate (CAGR)
 */
export function calculateCAGR(initialValue: number, finalValue: number, years: number): number {
  if (initialValue === 0 || years === 0) return 0;
  return (Math.pow(finalValue / initialValue, 1 / years) - 1) * 100;
}

/**
 * Calculate Sharpe ratio
 */
export function calculateSharpeRatio(returns: number[], riskFreeRate: number = 0.02): number {
  if (returns.length === 0) return 0;
  
  const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
  const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length;
  const stdDev = Math.sqrt(variance);
  
  if (stdDev === 0) return 0;
  return (avgReturn - riskFreeRate) / stdDev;
}

/**
 * Calculate maximum drawdown
 */
export function calculateMaxDrawdown(priceData: PriceData[]): number {
  if (priceData.length === 0) return 0;
  
  let maxDrawdown = 0;
  let peak = priceData[0].price;
  
  for (const data of priceData) {
    if (data.price > peak) {
      peak = data.price;
    }
    
    const drawdown = (peak - data.price) / peak;
    if (drawdown > maxDrawdown) {
      maxDrawdown = drawdown;
    }
  }
  
  return maxDrawdown * 100;
}

/**
 * Calculate win rate
 */
export function calculateWinRate(profitableTrades: number, totalTrades: number): number {
  if (totalTrades === 0) return 0;
  return (profitableTrades / totalTrades) * 100;
}

// ==============================================
// VALIDATION FUNCTIONS
// ==============================================

/**
 * Validate wallet address format
 */
export function isValidWalletAddress(address: string): boolean {
  // Basic Ethereum address validation
  const ethAddressRegex = /^0x[a-fA-F0-9]{40}$/;
  return ethAddressRegex.test(address);
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate investment amount
 */
export function isValidInvestmentAmount(amount: number, minAmount: number, maxAmount: number): boolean {
  return amount >= minAmount && amount <= maxAmount && amount > 0;
}

// ==============================================
// RISK ASSESSMENT FUNCTIONS
// ==============================================

/**
 * Get risk level color
 */
export function getRiskLevelColor(riskLevel: 'low' | 'medium' | 'high'): string {
  const colors = {
    low: 'text-green-600 bg-green-100',
    medium: 'text-yellow-600 bg-yellow-100',
    high: 'text-red-600 bg-red-100',
  };
  return colors[riskLevel];
}

/**
 * Calculate risk score based on bot performance
 */
export function calculateRiskScore(performance: BotPerformance): number {
  const volatilityWeight = 0.4;
  const drawdownWeight = 0.3;
  const sharpeWeight = 0.3;
  
  // Normalize metrics (0-100 scale)
  const volatilityScore = Math.min(performance.max_drawdown * 10, 100);
  const drawdownScore = Math.min(performance.max_drawdown * 5, 100);
  const sharpeScore = Math.max(0, Math.min((3 - performance.sharpe_ratio) * 33.33, 100));
  
  return volatilityWeight * volatilityScore + 
         drawdownWeight * drawdownScore + 
         sharpeWeight * sharpeScore;
}

// ==============================================
// SORTING AND FILTERING FUNCTIONS
// ==============================================

/**
 * Sort trading bots by performance
 */
export function sortBotsByPerformance(bots: TradingBot[], sortBy: string = 'return'): TradingBot[] {
  return [...bots].sort((a, b) => {
    switch (sortBy) {
      case 'return':
        return b.performance.total_return_percentage - a.performance.total_return_percentage;
      case 'winRate':
        return b.performance.win_rate - a.performance.win_rate;
      case 'sharpe':
        return b.performance.sharpe_ratio - a.performance.sharpe_ratio;
      case 'trades':
        return b.performance.total_trades - a.performance.total_trades;
      default:
        return 0;
    }
  });
}

/**
 * Filter bots by risk level
 */
export function filterBotsByRisk(bots: TradingBot[], riskLevel?: 'low' | 'medium' | 'high'): TradingBot[] {
  if (!riskLevel) return bots;
  return bots.filter(bot => bot.risk_level === riskLevel);
}

/**
 * Filter bots by minimum return
 */
export function filterBotsByReturn(bots: TradingBot[], minReturn: number): TradingBot[] {
  return bots.filter(bot => bot.performance.total_return_percentage >= minReturn);
}

// ==============================================
// DATE AND TIME FUNCTIONS
// ==============================================

/**
 * Format date for display
 */
export function formatDate(dateString: string, format: 'short' | 'long' = 'short'): string {
  const date = new Date(dateString);
  
  if (format === 'long') {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }
  
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  });
}

/**
 * Get relative time (e.g., "2 hours ago")
 */
export function getRelativeTime(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`;
  
  return formatDate(dateString);
}

// ==============================================
// CHART DATA HELPERS
// ==============================================

/**
 * Generate chart data for performance visualization
 */
export function generateChartData(priceData: PriceData[], label: string = 'Performance'): any {
  return {
    labels: priceData.map(data => new Date(data.timestamp).toLocaleDateString()),
    datasets: [
      {
        label,
        data: priceData.map(data => data.price),
        borderColor: 'rgb(236, 72, 153)',
        backgroundColor: 'rgba(236, 72, 153, 0.1)',
        fill: true,
        tension: 0.4,
      },
    ],
  };
}
