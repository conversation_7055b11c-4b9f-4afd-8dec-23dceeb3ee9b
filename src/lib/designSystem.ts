/**
 * ==============================================
 * MOREWISE DESIGN SYSTEM
 * ==============================================
 * 
 * Comprehensive design system for consistent UI/UX across the platform.
 * Replaces random values with systematic, professional design tokens.
 */

// ==============================================
// COLOR PALETTE
// ==============================================

export const colors = {
  // Primary Brand Colors
  primary: {
    50: '#faf5ff',
    100: '#f3e8ff',
    200: '#e9d5ff',
    300: '#d8b4fe',
    400: '#c084fc',
    500: '#a855f7', // Main purple
    600: '#9333ea',
    700: '#7c3aed',
    800: '#6b21a8',
    900: '#581c87',
  },
  
  // Secondary Brand Colors (Pink)
  secondary: {
    50: '#fdf2f8',
    100: '#fce7f3',
    200: '#fbcfe8',
    300: '#f9a8d4',
    400: '#f472b6',
    500: '#ec4899', // Main pink
    600: '#db2777',
    700: '#be185d',
    800: '#9d174d',
    900: '#831843',
  },
  
  // Accent Colors
  accent: {
    blue: '#3b82f6',
    green: '#10b981',
    orange: '#f59e0b',
    red: '#ef4444',
    yellow: '#eab308',
  },
  
  // Neutral Colors
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  },
  
  // Semantic Colors
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#3b82f6',
} as const;

// ==============================================
// SPACING SYSTEM (8px grid)
// ==============================================

export const spacing = {
  xs: '0.25rem',    // 4px
  sm: '0.5rem',     // 8px
  md: '0.75rem',    // 12px
  lg: '1rem',       // 16px
  xl: '1.25rem',    // 20px
  '2xl': '1.5rem',  // 24px
  '3xl': '2rem',    // 32px
  '4xl': '2.5rem',  // 40px
  '5xl': '3rem',    // 48px
  '6xl': '4rem',    // 64px
  '7xl': '5rem',    // 80px
  '8xl': '6rem',    // 96px
} as const;

// ==============================================
// TYPOGRAPHY SYSTEM
// ==============================================

export const typography = {
  fontFamily: {
    sans: ['Inter', 'system-ui', 'sans-serif'],
    mono: ['JetBrains Mono', 'monospace'],
  },
  
  fontSize: {
    xs: ['0.75rem', { lineHeight: '1rem' }],      // 12px
    sm: ['0.875rem', { lineHeight: '1.25rem' }],  // 14px
    base: ['1rem', { lineHeight: '1.5rem' }],     // 16px
    lg: ['1.125rem', { lineHeight: '1.75rem' }],  // 18px
    xl: ['1.25rem', { lineHeight: '1.75rem' }],   // 20px
    '2xl': ['1.5rem', { lineHeight: '2rem' }],    // 24px
    '3xl': ['1.875rem', { lineHeight: '2.25rem' }], // 30px
    '4xl': ['2.25rem', { lineHeight: '2.5rem' }],   // 36px
    '5xl': ['3rem', { lineHeight: '1' }],           // 48px
    '6xl': ['3.75rem', { lineHeight: '1' }],        // 60px
  },
  
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
    black: '900',
  },
} as const;

// ==============================================
// BORDER RADIUS SYSTEM
// ==============================================

export const borderRadius = {
  none: '0',
  sm: '0.25rem',    // 4px
  md: '0.375rem',   // 6px
  lg: '0.5rem',     // 8px
  xl: '0.75rem',    // 12px
  '2xl': '1rem',    // 16px
  '3xl': '1.5rem',  // 24px
  full: '9999px',
} as const;

// ==============================================
// SHADOW SYSTEM
// ==============================================

export const shadows = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
  
  // Glow effects for crypto theme
  glow: {
    purple: '0 0 20px rgba(168, 85, 247, 0.3)',
    pink: '0 0 20px rgba(236, 72, 153, 0.3)',
    blue: '0 0 20px rgba(59, 130, 246, 0.3)',
    green: '0 0 20px rgba(16, 185, 129, 0.3)',
  },
} as const;

// ==============================================
// COMPONENT VARIANTS
// ==============================================

export const components = {
  // Button variants
  button: {
    sizes: {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-base',
      lg: 'px-6 py-3 text-lg',
      xl: 'px-8 py-4 text-xl',
    },
    
    variants: {
      primary: 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600',
      secondary: 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white hover:from-blue-600 hover:to-cyan-600',
      success: 'bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:from-green-600 hover:to-emerald-600',
      warning: 'bg-gradient-to-r from-orange-500 to-yellow-500 text-white hover:from-orange-600 hover:to-yellow-600',
      danger: 'bg-gradient-to-r from-red-500 to-pink-500 text-white hover:from-red-600 hover:to-pink-600',
      ghost: 'bg-transparent border-2 border-purple-500 text-purple-500 hover:bg-purple-500 hover:text-white',
      glass: 'backdrop-blur-sm bg-white/10 border border-white/20 text-gray-900 hover:bg-white/20',
    },
  },
  
  // Card variants
  card: {
    variants: {
      default: 'bg-white rounded-2xl shadow-lg border border-gray-100',
      glass: 'backdrop-blur-sm bg-white/10 rounded-2xl border border-white/20',
      gradient: 'bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-2xl border border-purple-200',
      elevated: 'bg-white rounded-2xl shadow-xl border border-gray-100',
    },
  },
  
  // Input variants
  input: {
    variants: {
      default: 'w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20',
      glass: 'w-full px-4 py-3 rounded-xl backdrop-blur-sm bg-white/10 border border-white/20 focus:border-white/40',
    },
  },
} as const;

// ==============================================
// ANIMATION SYSTEM
// ==============================================

export const animations = {
  transition: {
    fast: 'transition-all duration-150 ease-in-out',
    normal: 'transition-all duration-200 ease-in-out',
    slow: 'transition-all duration-300 ease-in-out',
    smooth: 'transition-all duration-300 cubic-bezier(0.4, 0, 0.2, 1)',
  },
  
  hover: {
    scale: 'hover:scale-105 transform transition-transform duration-200',
    lift: 'hover:-translate-y-1 transition-transform duration-200',
    glow: 'hover:shadow-glow transition-shadow duration-200',
  },
} as const;

// ==============================================
// BREAKPOINTS
// ==============================================

export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;

// ==============================================
// UTILITY FUNCTIONS
// ==============================================

export const utils = {
  // Generate consistent spacing classes
  spacing: (size: keyof typeof spacing) => spacing[size],
  
  // Generate consistent color classes
  color: (color: string, shade?: number) => {
    if (shade) {
      return `${color}-${shade}`;
    }
    return color;
  },
  
  // Generate consistent gradient classes
  gradient: (from: string, to: string) => `bg-gradient-to-r from-${from} to-${to}`,
} as const;
