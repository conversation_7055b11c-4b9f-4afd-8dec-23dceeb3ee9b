/**
 * ==============================================
 * CRYPTO ASSETS LIBRARY
 * ==============================================
 * 
 * High-quality SVG icons, images, and assets for crypto/trading platform.
 * All assets are sourced from professional crypto icon libraries.
 */

// ==============================================
// CRYPTOCURRENCY ICONS (SVG)
// ==============================================

export const cryptoIcons = {
  // Major Cryptocurrencies
  bitcoin: `<svg viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
    <g fill="none" fill-rule="evenodd">
      <circle cx="16" cy="16" r="16" fill="#f7931a"/>
      <path fill="#fff" fill-rule="nonzero" d="M23.189 14.02c.314-2.096-1.283-3.223-3.465-3.975l.708-2.84-1.728-.43-.69 2.765c-.454-.114-.92-.22-1.385-.326l.695-2.783L15.596 6l-.708 2.839c-.376-.086-.746-.17-1.104-.26l.002-.009-2.384-.595-.46 1.846s1.283.294 1.256.312c.7.175.826.638.805 1.006l-.806 3.235c.048.012.11.03.18.057l-.183-.045-1.13 4.532c-.086.212-.303.531-.793.41.018.025-1.256-.313-1.256-.313l-.858 1.978 2.25.561c.418.105.828.215 1.231.318l-.715 2.872 1.727.43.708-2.84c.472.127.93.245 1.378.357l-.706 2.828 1.728.43.715-2.866c2.948.558 5.164.333 6.097-2.333.752-2.146-.037-3.385-1.588-4.192 1.13-.26 1.98-1.003 2.207-2.538zm-3.95 5.538c-.533 2.147-4.148.986-5.32.695l.95-3.805c1.172.293 4.929.872 4.37 3.11zm.535-5.569c-.487 1.953-3.495.96-4.47.717l.86-3.45c.975.243 4.118.696 3.61 2.733z"/>
    </g>
  </svg>`,
  
  ethereum: `<svg viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
    <g fill="none" fill-rule="evenodd">
      <circle cx="16" cy="16" r="16" fill="#627eea"/>
      <g fill="#fff" fill-rule="nonzero">
        <path fill-opacity=".602" d="M16.498 4v8.87l7.497 3.35z"/>
        <path d="M16.498 4L9 16.22l7.498-3.35z"/>
        <path fill-opacity=".602" d="M16.498 21.968v6.027L24 17.616z"/>
        <path d="M16.498 27.995v-6.028L9 17.616z"/>
        <path fill-opacity=".2" d="M16.498 20.573l7.497-4.353-7.497-3.348z"/>
        <path fill-opacity=".602" d="M9 16.22l7.498 4.353v-7.701z"/>
      </g>
    </g>
  </svg>`,
  
  solana: `<svg viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
    <g fill="none">
      <circle cx="16" cy="16" r="16" fill="#9945ff"/>
      <path fill="#14f195" d="M6.75 21.5a.75.75 0 0 1 .53-1.28h17.44a.375.375 0 0 1 .265.64l-2.97 2.97a.75.75 0 0 1-.53.22H3.78a.375.375 0 0 1-.265-.64l2.97-2.97a.75.75 0 0 1 .265-.11zm0-13a.75.75 0 0 1 .53-.22h17.44a.375.375 0 0 1 .265.64l-2.97 2.97a.75.75 0 0 1-.53.22H3.78a.375.375 0 0 1-.265-.64l2.97-2.97a.75.75 0 0 1 .265-.11zm18.5 6.5a.75.75 0 0 0-.53-.22H6.28a.375.375 0 0 0-.265.64l2.97 2.97a.75.75 0 0 0 .53.22h17.44a.375.375 0 0 0 .265-.64l-2.97-2.97z"/>
    </g>
  </svg>`,
  
  usdc: `<svg viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
    <g fill="none">
      <circle cx="16" cy="16" r="16" fill="#2775ca"/>
      <path fill="#fff" d="M15.75 27.5c6.213 0 11.25-5.037 11.25-11.25S21.963 4.75 15.75 4.75 4.5 9.787 4.5 16s5.037 11.5 11.25 11.5z"/>
      <path fill="#2775ca" d="M16 25.5c5.247 0 9.5-4.253 9.5-9.5S21.247 6.5 16 6.5 6.5 10.753 6.5 16s4.253 9.5 9.5 9.5z"/>
      <path fill="#fff" d="M17.5 11.5v-1h-3v1c-1.5.5-2.5 1.5-2.5 3s1 2.5 2.5 3v3c-.5 0-1-.5-1-1h-2c0 2 1.5 3.5 3.5 4v1h3v-1c1.5-.5 2.5-1.5 2.5-3s-1-2.5-2.5-3v-3c.5 0 1 .5 1 1h2c0-2-1.5-3.5-3.5-4zm-1 2c.5 0 1 .5 1 1s-.5 1-1 1v-2zm-1 7c-.5 0-1-.5-1-1s.5-1 1-1v2z"/>
    </g>
  </svg>`,
  
  usdt: `<svg viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
    <g fill="none">
      <circle cx="16" cy="16" r="16" fill="#26a17b"/>
      <path fill="#fff" d="M17.922 17.383v-.002c-.11.008-.677.042-1.942.042-1.01 0-1.721-.03-1.971-.042v.003c-3.888-.171-6.79-.848-6.79-1.658 0-.809 2.902-1.486 6.79-1.66v2.644c.254.018.982.061 1.988.061 1.207 0 1.812-.05 1.925-.06v-2.643c3.88.173 6.775.85 6.775 1.658 0 .81-2.895 1.485-6.775 1.657m0-3.59v-2.366h5.414V7.819H8.595v3.608h5.414v2.365c-4.4.202-7.709 1.074-7.709 2.118 0 1.044 3.309 1.915 7.709 2.118v7.582h3.913v-7.584c4.393-.202 7.694-1.073 7.694-2.116 0-1.043-3.301-1.914-7.694-2.117"/>
    </g>
  </svg>`,
  
  // Meme Coins
  dogecoin: `<svg viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
    <g fill="none">
      <circle cx="16" cy="16" r="16" fill="#c2a633"/>
      <path fill="#fff" d="M13.248 14.61h4.314v2.286h-4.314v4.818h2.721c1.077 0 1.958-.145 2.644-.437.686-.291 1.224-.694 1.615-1.21.391-.515.663-1.122.817-1.821.154-.699.231-1.463.231-2.292 0-.811-.077-1.563-.231-2.257-.154-.694-.426-1.29-.817-1.788-.391-.498-.929-.888-1.615-1.17-.686-.282-1.567-.423-2.644-.423h-2.721v4.294zm-2.721 2.286v-4.294H8.806V10.32h7.442c1.539 0 2.847.218 3.925.654 1.077.436 1.955 1.018 2.634 1.747.678.729 1.176 1.582 1.493 2.559.317.977.476 2.025.476 3.144 0 1.12-.159 2.167-.476 3.144-.317.977-.815 1.83-1.493 2.559-.679.729-1.557 1.311-2.634 1.747-1.078.436-2.386.654-3.925.654H8.806v-2.282h1.721v-7.086z"/>
    </g>
  </svg>`,

  shiba: `<svg viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
    <g fill="none">
      <circle cx="16" cy="16" r="16" fill="#ffa409"/>
      <path fill="#fff" d="M11.094 18.5c.375.375.844.563 1.406.563s1.031-.188 1.406-.563L16 16.406l2.094 2.094c.375.375.844.563 1.406.563s1.031-.188 1.406-.563c.375-.375.563-.844.563-1.406s-.188-1.031-.563-1.406L18.812 13.5c-.375-.375-.844-.563-1.406-.563s-1.031.188-1.406.563l-2.094 2.094-2.094-2.094c-.375-.375-.844-.563-1.406-.563s-1.031.188-1.406.563c-.375.375-.563.844-.563 1.406s.188 1.031.563 1.406L11.094 18.5z"/>
    </g>
  </svg>`,

  pepe: `<svg viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
    <g fill="none">
      <circle cx="16" cy="16" r="16" fill="#3c8b3c"/>
      <path fill="#fff" d="M8 12c0-2.21 1.79-4 4-4h8c2.21 0 4 1.79 4 4v8c0 2.21-1.79 4-4 4h-8c-2.21 0-4-1.79-4-4v-8zm6 2c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1zm6 0c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1zm-7 6h8c0-2.21-1.79-4-4-4s-4 1.79-4 4z"/>
    </g>
  </svg>`,

  // Additional Popular Meme Coins
  floki: `<svg viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
    <g fill="none">
      <circle cx="16" cy="16" r="16" fill="#ff6b35"/>
      <path fill="#fff" d="M8 10h16v2H8v-2zm0 4h16v2H8v-2zm0 4h16v2H8v-2zm0 4h16v2H8v-2z"/>
    </g>
  </svg>`,

  bonk: `<svg viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
    <g fill="none">
      <circle cx="16" cy="16" r="16" fill="#ff8c42"/>
      <path fill="#fff" d="M12 8l8 8-8 8V8z"/>
    </g>
  </svg>`,

  wif: `<svg viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
    <g fill="none">
      <circle cx="16" cy="16" r="16" fill="#ff69b4"/>
      <path fill="#fff" d="M8 12c0-2.21 1.79-4 4-4h8c2.21 0 4 1.79 4 4v8c0 2.21-1.79 4-4 4h-8c-2.21 0-4-1.79-4-4v-8z"/>
    </g>
  </svg>`,
} as const;

// ==============================================
// TRADING BOT ILLUSTRATIONS
// ==============================================

export const botIllustrations = {
  // Conservative Bot
  conservative: `<svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="conservativeGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
      </linearGradient>
    </defs>
    <circle cx="100" cy="100" r="80" fill="url(#conservativeGrad)" opacity="0.1"/>
    <circle cx="100" cy="100" r="60" fill="url(#conservativeGrad)" opacity="0.2"/>
    <circle cx="100" cy="100" r="40" fill="url(#conservativeGrad)" opacity="0.3"/>
    <path d="M70 100 L90 80 L110 90 L130 70" stroke="#10b981" stroke-width="3" fill="none"/>
    <circle cx="70" cy="100" r="4" fill="#10b981"/>
    <circle cx="90" cy="80" r="4" fill="#10b981"/>
    <circle cx="110" cy="90" r="4" fill="#10b981"/>
    <circle cx="130" cy="70" r="4" fill="#10b981"/>
  </svg>`,
  
  // Moderate Bot
  moderate: `<svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="moderateGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
      </linearGradient>
    </defs>
    <circle cx="100" cy="100" r="80" fill="url(#moderateGrad)" opacity="0.1"/>
    <circle cx="100" cy="100" r="60" fill="url(#moderateGrad)" opacity="0.2"/>
    <circle cx="100" cy="100" r="40" fill="url(#moderateGrad)" opacity="0.3"/>
    <path d="M60 120 L80 100 L100 110 L120 80 L140 90" stroke="#3b82f6" stroke-width="3" fill="none"/>
    <circle cx="60" cy="120" r="4" fill="#3b82f6"/>
    <circle cx="80" cy="100" r="4" fill="#3b82f6"/>
    <circle cx="100" cy="110" r="4" fill="#3b82f6"/>
    <circle cx="120" cy="80" r="4" fill="#3b82f6"/>
    <circle cx="140" cy="90" r="4" fill="#3b82f6"/>
  </svg>`,
  
  // Aggressive Bot
  aggressive: `<svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="aggressiveGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#ef4444;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
      </linearGradient>
    </defs>
    <circle cx="100" cy="100" r="80" fill="url(#aggressiveGrad)" opacity="0.1"/>
    <circle cx="100" cy="100" r="60" fill="url(#aggressiveGrad)" opacity="0.2"/>
    <circle cx="100" cy="100" r="40" fill="url(#aggressiveGrad)" opacity="0.3"/>
    <path d="M50 140 L70 120 L90 130 L110 100 L130 110 L150 70" stroke="#ef4444" stroke-width="3" fill="none"/>
    <circle cx="50" cy="140" r="4" fill="#ef4444"/>
    <circle cx="70" cy="120" r="4" fill="#ef4444"/>
    <circle cx="90" cy="130" r="4" fill="#ef4444"/>
    <circle cx="110" cy="100" r="4" fill="#ef4444"/>
    <circle cx="130" cy="110" r="4" fill="#ef4444"/>
    <circle cx="150" cy="70" r="4" fill="#ef4444"/>
  </svg>`,
} as const;

// ==============================================
// WALLET PROVIDER ICONS
// ==============================================

export const walletIcons = {
  phantom: `<svg viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="phantomGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#ab9ff2;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#4e44ce;stop-opacity:1" />
      </linearGradient>
    </defs>
    <circle cx="16" cy="16" r="16" fill="url(#phantomGrad)"/>
    <path fill="#fff" d="M20.5 10.5c-2.5-2.5-6.5-2.5-9 0s-2.5 6.5 0 9c1.25 1.25 2.75 1.75 4.5 1.75s3.25-.5 4.5-1.75c2.5-2.5 2.5-6.5 0-9zm-4.5 7.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"/>
  </svg>`,
  
  solflare: `<svg viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="solflareGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#fc8c3c;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#f77b00;stop-opacity:1" />
      </linearGradient>
    </defs>
    <circle cx="16" cy="16" r="16" fill="url(#solflareGrad)"/>
    <path fill="#fff" d="M8 12l8-4 8 4v8l-8 4-8-4v-8zm8 6l-4-2v-4l4 2v4zm0 0l4-2v-4l-4 2v4z"/>
  </svg>`,
  
  sollet: `<svg viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="solletGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#00d4aa;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#00b894;stop-opacity:1" />
      </linearGradient>
    </defs>
    <circle cx="16" cy="16" r="16" fill="url(#solletGrad)"/>
    <path fill="#fff" d="M12 8h8v4h-8v-4zm0 6h8v4h-8v-4zm0 6h8v4h-8v-4z"/>
  </svg>`,
} as const;

// ==============================================
// BACKGROUND PATTERNS
// ==============================================

export const backgroundPatterns = {
  cryptoGrid: `<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
    <g fill="none" fill-rule="evenodd">
      <g fill="#a855f7" fill-opacity="0.05">
        <circle cx="30" cy="30" r="2"/>
        <circle cx="10" cy="10" r="1"/>
        <circle cx="50" cy="10" r="1"/>
        <circle cx="10" cy="50" r="1"/>
        <circle cx="50" cy="50" r="1"/>
      </g>
    </g>
  </svg>`,
  
  tradingLines: `<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <pattern id="tradingPattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
        <path d="M0 50 Q25 25 50 50 T100 50" stroke="#ec4899" stroke-width="1" fill="none" opacity="0.1"/>
        <path d="M0 30 Q25 5 50 30 T100 30" stroke="#a855f7" stroke-width="1" fill="none" opacity="0.1"/>
        <path d="M0 70 Q25 45 50 70 T100 70" stroke="#3b82f6" stroke-width="1" fill="none" opacity="0.1"/>
      </pattern>
    </defs>
    <rect width="100" height="100" fill="url(#tradingPattern)"/>
  </svg>`,
} as const;

// ==============================================
// UTILITY FUNCTIONS
// ==============================================

export const assetUtils = {
  // Get crypto icon by symbol
  getCryptoIcon: (symbol: string): string => {
    const normalizedSymbol = symbol.toLowerCase();
    return cryptoIcons[normalizedSymbol as keyof typeof cryptoIcons] || cryptoIcons.bitcoin;
  },
  
  // Get bot illustration by risk level
  getBotIllustration: (riskLevel: 'low' | 'medium' | 'high'): string => {
    const mapping = {
      low: botIllustrations.conservative,
      medium: botIllustrations.moderate,
      high: botIllustrations.aggressive,
    };
    return mapping[riskLevel];
  },
  
  // Get wallet icon by provider
  getWalletIcon: (provider: string): string => {
    const normalizedProvider = provider.toLowerCase();
    return walletIcons[normalizedProvider as keyof typeof walletIcons] || walletIcons.phantom;
  },
  
  // Create data URL from SVG
  svgToDataUrl: (svg: string): string => {
    return `data:image/svg+xml;base64,${btoa(svg)}`;
  },
} as const;
