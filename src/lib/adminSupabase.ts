/**
 * ==============================================
 * ADMIN SUPABASE OPERATIONS
 * ==============================================
 * 
 * Database operations for admin functionality
 */

import { supabase } from './supabase';

// ==============================================
// ADMIN AUTHENTICATION
// ==============================================

export async function adminSignIn(email: string, password: string) {
  try {
    // Simple admin login - just authenticate with Supabase
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (authError) {
      return { data: null, error: authError.message };
    }

    if (!authData.user) {
      return { data: null, error: 'Authentication failed' };
    }

    // Check if admin user exists, if not create one
    let { data: adminUser, error: adminError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('email', email)
      .single();

    if (adminError || !adminUser) {
      // Create admin user record
      const { data: newAdminUser, error: createError } = await supabase
        .from('admin_users')
        .insert({
          email: email,
          username: email.split('@')[0],
          name: 'System Administrator',
          role: 'super_admin',
          permissions: { all: true },
          is_active: true,
          last_login: new Date().toISOString()
        })
        .select()
        .single();

      if (createError) {
        console.error('Failed to create admin user:', createError);
        return { data: null, error: 'Failed to create admin account.' };
      }

      adminUser = newAdminUser;
    } else {
      // Update last login
      await supabase
        .from('admin_users')
        .update({ last_login: new Date().toISOString() })
        .eq('id', adminUser.id);
    }

    return {
      data: {
        session: authData.session,
        user: authData.user,
        adminUser
      },
      error: null
    };
  } catch (error) {
    console.error('Admin signin error:', error);
    return { data: null, error: 'Authentication failed' };
  }
}



export async function getAdminSession() {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();

    if (error || !session) {
      return { session: null, adminUser: null, error };
    }

    // Get admin user data
    const { data: adminUser, error: adminError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('email', session.user.email)
      .eq('is_active', true)
      .single();

    if (adminError || !adminUser) {
      return { session: null, adminUser: null, error: 'Admin access denied' };
    }

    return { session, adminUser, error: null };
  } catch (error) {
    console.error('Get admin session error:', error);
    return { session: null, adminUser: null, error };
  }
}

// ==============================================
// DASHBOARD STATISTICS
// ==============================================

export async function getDashboardStats() {
  try {
    const [
      { count: totalUsers },
      { count: totalBots },
      { count: activeTrades },
      { count: totalTransactions }
    ] = await Promise.all([
      supabase.from('users').select('*', { count: 'exact', head: true }),
      supabase.from('trading_bots').select('*', { count: 'exact', head: true }),
      supabase.from('trades').select('*', { count: 'exact', head: true }).eq('status', 'completed'),
      supabase.from('wallet_transactions').select('*', { count: 'exact', head: true })
    ]);

    // Get total platform value
    const { data: portfolios } = await supabase
      .from('portfolios')
      .select('total_balance');

    const totalValue = portfolios?.reduce((sum, p) => sum + (parseFloat(p.total_balance) || 0), 0) || 0;

    // Get recent activity
    const { data: recentTrades } = await supabase
      .from('trades')
      .select(`
        *,
        users(name, email),
        trading_bots(name)
      `)
      .order('created_at', { ascending: false })
      .limit(10);

    return {
      data: {
        totalUsers: totalUsers || 0,
        totalBots: totalBots || 0,
        activeTrades: activeTrades || 0,
        totalTransactions: totalTransactions || 0,
        totalValue,
        recentTrades: recentTrades || []
      },
      error: null
    };
  } catch (error) {
    console.error('Dashboard stats error:', error);
    return { data: null, error };
  }
}

// ==============================================
// USER MANAGEMENT
// ==============================================

export async function getUsers(page = 1, limit = 20, search = '') {
  try {
    let query = supabase
      .from('users')
      .select(`
        *,
        portfolios(total_balance, total_profit_loss),
        bot_investments(amount, profit_loss),
        referrals_as_referrer:referrals!referrer_id(referred_id, total_commission),
        referrals_as_referred:referrals!referred_id(referrer_id, referral_code)
      `, { count: 'exact' });

    if (search) {
      query = query.or(`name.ilike.%${search}%,email.ilike.%${search}%,username.ilike.%${search}%`);
    }

    const { data, error, count } = await query
      .order('created_at', { ascending: false })
      .range((page - 1) * limit, page * limit - 1);

    return { data, error, count };
  } catch (error) {
    console.error('Get users error:', error);
    return { data: null, error, count: 0 };
  }
}

export async function updateUserStatus(userId: string, updates: any) {
  try {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single();

    return { data, error };
  } catch (error) {
    console.error('Update user status error:', error);
    return { data: null, error };
  }
}

export async function updateUserBalance(userId: string, newBalance: number) {
  try {
    const { data, error } = await supabase
      .from('portfolios')
      .update({ 
        total_balance: newBalance,
        available_balance: newBalance,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .select()
      .single();

    return { data, error };
  } catch (error) {
    console.error('Update user balance error:', error);
    return { data: null, error };
  }
}

// ==============================================
// TRADING BOTS MANAGEMENT
// ==============================================

export async function getTradingBots() {
  try {
    const { data, error } = await supabase
      .from('trading_bots')
      .select(`
        *,
        admin_users(name),
        bot_investments(amount, status, profit_loss)
      `)
      .order('created_at', { ascending: false });

    return { data, error };
  } catch (error) {
    console.error('Get trading bots error:', error);
    return { data: null, error };
  }
}

export async function createTradingBot(botData: any) {
  try {
    const { data, error } = await supabase
      .from('trading_bots')
      .insert(botData)
      .select()
      .single();

    return { data, error };
  } catch (error) {
    console.error('Create trading bot error:', error);
    return { data: null, error };
  }
}

export async function updateTradingBot(botId: string, updates: any) {
  try {
    const { data, error } = await supabase
      .from('trading_bots')
      .update(updates)
      .eq('id', botId)
      .select()
      .single();

    return { data, error };
  } catch (error) {
    console.error('Update trading bot error:', error);
    return { data: null, error };
  }
}

// ==============================================
// TRANSACTIONS MANAGEMENT
// ==============================================

export async function getTransactions(page = 1, limit = 50) {
  try {
    const { data, error, count } = await supabase
      .from('wallet_transactions')
      .select(`
        *,
        users(name, email, username)
      `, { count: 'exact' })
      .order('created_at', { ascending: false })
      .range((page - 1) * limit, page * limit - 1);

    return { data, error, count };
  } catch (error) {
    console.error('Get transactions error:', error);
    return { data: null, error, count: 0 };
  }
}

export async function approveTransaction(transactionId: string) {
  try {
    const { data, error } = await supabase
      .from('wallet_transactions')
      .update({ status: 'completed' })
      .eq('id', transactionId)
      .select()
      .single();

    return { data, error };
  } catch (error) {
    console.error('Approve transaction error:', error);
    return { data: null, error };
  }
}

// ==============================================
// SUPPORT CHAT MANAGEMENT
// ==============================================

export async function getSupportChats() {
  try {
    const { data, error } = await supabase
      .from('support_chats')
      .select(`
        *,
        users(name, email),
        admin_users(name),
        support_messages(message, sender_type, created_at)
      `)
      .order('updated_at', { ascending: false });

    return { data, error };
  } catch (error) {
    console.error('Get support chats error:', error);
    return { data: null, error };
  }
}

export async function sendSupportMessage(chatId: string, adminId: string, message: string) {
  try {
    const { data, error } = await supabase
      .from('support_messages')
      .insert({
        chat_id: chatId,
        sender_id: adminId,
        sender_type: 'admin',
        message
      })
      .select()
      .single();

    // Update chat timestamp
    await supabase
      .from('support_chats')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', chatId);

    return { data, error };
  } catch (error) {
    console.error('Send support message error:', error);
    return { data: null, error };
  }
}

// ==============================================
// AUDIT LOGGING
// ==============================================

export async function createAuditLog(adminId: string, action: string, details: any) {
  try {
    const { data, error } = await supabase
      .from('audit_logs')
      .insert({
        admin_id: adminId,
        action,
        ...details
      });

    return { data, error };
  } catch (error) {
    console.error('Create audit log error:', error);
    return { data: null, error };
  }
}

export async function getAuditLogs(page = 1, limit = 50) {
  try {
    const { data, error, count } = await supabase
      .from('audit_logs')
      .select(`
        *,
        admin_users(name, email)
      `, { count: 'exact' })
      .order('created_at', { ascending: false })
      .range((page - 1) * limit, page * limit - 1);

    return { data, error, count };
  } catch (error) {
    console.error('Get audit logs error:', error);
    return { data: null, error, count: 0 };
  }
}
