/**
 * ==============================================
 * AUTHENTICATION UTILITIES
 * ==============================================
 *
 * This file contains authentication functions for the crypto trading platform.
 * Supports both traditional email/password and wallet-based authentication.
 */

import { User, AdminSession } from '../types';
import {
  supabase,
  signUpWithEmail,
  signInWithEmail,
  signOut as supabaseSignOut,
  getCurrentSession,
  getCurrentUser
} from './supabase';

// ==============================================
// TYPES
// ==============================================

export interface LoginResponse {
  session?: {
    token: string;
    user_id: string;
    expires_at: string;
  };
  error?: {
    message: string;
  };
}

export interface WalletLoginResponse {
  session?: {
    token: string;
    user_id: string;
    wallet_address: string;
    expires_at: string;
  };
  error?: {
    message: string;
  };
}

export interface SignupResponse {
  user?: {
    id: string;
    email: string;
  };
  error?: {
    message: string;
  };
}

// ==============================================
// PRODUCTION AUTHENTICATION
// ==============================================

// ==============================================
// AUTHENTICATION FUNCTIONS
// ==============================================

/**
 * Email/password login using Supabase
 */
export async function login(username: string, password: string): Promise<LoginResponse> {
  try {
    // Use Supabase for authentication
    const { data, error } = await signInWithEmail(username, password);

    if (error) {
      return {
        error: {
          message: error.message || 'Invalid email or password',
        },
      };
    }

    if (data.session && data.user) {
      return {
        session: {
          token: data.session.access_token,
          user_id: data.user.id,
          expires_at: data.session.expires_at ? new Date(data.session.expires_at * 1000).toISOString() : new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        },
      };
    }

    return {
      error: {
        message: 'Authentication failed. Please try again.',
      },
    };
  } catch (error) {
    console.error('Login error:', error);
    return {
      error: {
        message: 'Login failed. Please try again.',
      },
    };
  }
}

/**
 * Sign up new user with email and password
 */
export async function signup(
  email: string,
  password: string,
  name: string,
  username: string,
  referralCode?: string
): Promise<SignupResponse> {
  try {
    // Prepare user metadata
    const metadata = {
      name,
      username,
      referral_code: referralCode || null,
      created_at: new Date().toISOString()
    };

    const { data, error } = await signUpWithEmail(email, password, metadata);

    if (error) {
      return {
        error: {
          message: error.message || 'Signup failed. Please try again.',
        },
      };
    }

    if (data.user) {
      return {
        user: {
          id: data.user.id,
          email: data.user.email || email,
        },
      };
    }

    return {
      error: {
        message: 'Signup failed. Please try again.',
      },
    };
  } catch (error) {
    return {
      error: {
        message: 'Signup failed. Please try again.',
      },
    };
  }
}

/**
 * Wallet-based authentication (placeholder for future implementation)
 */
export async function loginWithWallet(walletAddress: string, signature: string): Promise<WalletLoginResponse> {
  return {
    error: {
      message: 'Wallet authentication not yet implemented.',
    },
  };
}

/**
 * Get current user session from Supabase
 */
export async function getSession(token?: string): Promise<{ user_id: string } | null> {
  try {
    // Get session from Supabase
    const { session, error } = await getCurrentSession();

    if (error || !session) {
      return null;
    }

    return { user_id: session.user.id };
  } catch (error) {
    console.error('Session validation error:', error);
    return null;
  }
}

/**
 * Logout user
 */
export async function logout(): Promise<void> {
  try {
    // Sign out from Supabase
    await supabaseSignOut();

    // Clear local storage
    localStorage.removeItem('token');
    localStorage.removeItem('wallet_address');
  } catch (error) {
    console.error('Logout error:', error);
  }
}

/**
 * Admin login (redirects to dedicated admin system)
 */
export async function adminLogin(email: string, password: string): Promise<{ session?: AdminSession; error?: string }> {
  return {
    error: 'Please use the dedicated admin login at /admin',
  };
}

/**
 * Get admin session
 */
export async function getAdminSession(): Promise<AdminSession | null> {
  try {
    const token = localStorage.getItem('admin_token');
    if (!token) return null;

    await new Promise(resolve => setTimeout(resolve, 300));

    // Admin session validation removed - use dedicated admin system

    return null;
  } catch (error) {
    return null;
  }
}

/**
 * Admin logout
 */
export async function adminLogout(): Promise<void> {
  try {
    localStorage.removeItem('admin_token');
    await new Promise(resolve => setTimeout(resolve, 300));
  } catch (error) {
    console.error('Admin logout error:', error);
  }
}

// ==============================================
// UTILITY FUNCTIONS
// ==============================================



/**
 * Check if user is authenticated
 */
export function isAuthenticated(): boolean {
  const token = localStorage.getItem('token');
  return !!token;
}

/**
 * Get stored wallet address
 */
export function getStoredWalletAddress(): string | null {
  return localStorage.getItem('wallet_address');
}

/**
 * Store wallet address
 */
export function storeWalletAddress(address: string): void {
  localStorage.setItem('wallet_address', address);
}
