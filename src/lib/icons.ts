/**
 * ==============================================
 * FONTAWESOME ICON CONFIGURATION
 * ==============================================
 * 
 * This file configures FontAwesome icons for the crypto trading platform.
 * Replaces Lucide React icons with FontAwesome for better crypto-themed icons.
 */

import { library } from '@fortawesome/fontawesome-svg-core';
import {
  // Navigation and UI
  faHome,
  faRobot,
  faWallet,
  faChartLine,
  faCog,
  faSignOutAlt,
  faBars,
  faTimes,
  faUser,
  faLock,
  faEye,
  faEyeSlash,
  faArrowLeft,
  faArrowRight,

  // Crypto and Trading
  faCoins,
  faArrowUp,
  faArrowDown,
  faExchangeAlt,
  faBalanceScale,
  faChartArea,
  faDollarSign,

  // Actions and Features
  faPlay,
  faPause,
  faStop,
  faDownload,
  faUpload,
  faSearch,
  faFilter,
  faSort,
  faPlus,
  faMinus,
  faEdit,
  faTrash,
  faSave,
  faRefresh,
  faSync,

  // Status and Indicators
  faCheckCircle,
  faTimesCircle,
  faExclamationTriangle,
  faInfoCircle,
  faSpinner,
  faClock,
  faCalendar,
  faStar,
  faHeart,
  faCircle,

  // Security and Trust
  faShield,
  faLockOpen,
  faKey,
  faCertificate,
  faAward,
  faFingerprint,

  // Communication
  faEnvelope,
  faPhone,
  faBell,
  faComment,
  faQuestionCircle,
  faComments,

  // Data and Analytics
  faChartBar,
  faChartPie,
  faTable,
  faDatabase,
  faFileExport,
  faFileImport,

  // Social and External
  faExternalLinkAlt,
  faShare,
  faCopy,
  faLink,

  // Investment & Finance
  faPiggyBank,
  faHandHoldingUsd,
  faMoneyBillWave,
  faLandmark,
  faCreditCard,
  faReceipt,
  faCalculator,

  // Misc
  faGlobe,
  faMapMarker,
  faBuilding,
  faIndustry,
  faRocket,
  faGem,
  faFire,
  faBolt,
  faMagic,
  faGift,
  faQrcode,
  faPaperPlane,
  faFileAlt,
  faFolder,
  faCube,
  faLayerGroup,
  faNetworkWired,
} from '@fortawesome/free-solid-svg-icons';



// Add icons to the library
library.add(
  // Navigation and UI
  faHome,
  faRobot,
  faWallet,
  faChartLine,
  faCog,
  faSignOutAlt,
  faBars,
  faTimes,
  faUser,
  faLock,
  faEye,
  faEyeSlash,
  faArrowLeft,
  faArrowRight,

  // Crypto and Trading
  faCoins,
  faArrowUp,
  faArrowDown,
  faExchangeAlt,
  faBalanceScale,
  faChartArea,
  faDollarSign,

  // Actions and Features
  faPlay,
  faPause,
  faStop,
  faDownload,
  faUpload,
  faSearch,
  faFilter,
  faSort,
  faPlus,
  faMinus,
  faEdit,
  faTrash,
  faSave,
  faRefresh,
  faSync,

  // Status and Indicators
  faCheckCircle,
  faTimesCircle,
  faExclamationTriangle,
  faInfoCircle,
  faSpinner,
  faClock,
  faCalendar,
  faStar,
  faHeart,
  faCircle,

  // Security and Trust
  faShield,
  faLockOpen,
  faKey,
  faCertificate,
  faAward,
  faFingerprint,

  // Communication
  faEnvelope,
  faPhone,
  faBell,
  faComment,
  faQuestionCircle,
  faComments,

  // Data and Analytics
  faChartBar,
  faChartPie,
  faTable,
  faDatabase,
  faFileExport,
  faFileImport,

  // Social and External
  faExternalLinkAlt,
  faShare,
  faCopy,
  faLink,

  // Investment & Finance
  faPiggyBank,
  faHandHoldingUsd,
  faMoneyBillWave,
  faLandmark,
  faCreditCard,
  faReceipt,
  faCalculator,

  // Misc
  faGlobe,
  faMapMarker,
  faBuilding,
  faIndustry,
  faRocket,
  faGem,
  faFire,
  faBolt,
  faMagic,
  faGift,
  faQrcode,
  faPaperPlane,
  faFileAlt,
  faFolder,
  faCube,
  faLayerGroup,
  faNetworkWired,
);

// Export commonly used icon names for easy reference
export const ICON_NAMES = {
  // Navigation
  HOME: 'home',
  ROBOT: 'robot',
  WALLET: 'wallet',
  CHART: 'chart-line',
  CHART_AREA: 'chart-area',
  SETTINGS: 'cog',
  LOGOUT: 'sign-out-alt',
  MENU: 'bars',
  CLOSE: 'times',
  BACK: 'arrow-left',
  FORWARD: 'arrow-right',

  // User & Auth
  USER: 'user',
  LOCK: 'lock',
  UNLOCK: 'lock-open',
  EYE: 'eye',
  EYE_SLASH: 'eye-slash',
  FINGERPRINT: 'fingerprint',

  // Crypto & Trading
  BITCOIN: 'coins',
  ETHEREUM: 'coins',
  SOLANA: 'coins',
  USDC: 'coins',
  USDT: 'coins',
  COINS: 'coins',
  TRENDING_UP: 'arrow-up',
  TRENDING_DOWN: 'arrow-down',
  ARROW_UP: 'arrow-up',
  ARROW_DOWN: 'arrow-down',
  EXCHANGE: 'exchange-alt',
  BALANCE: 'balance-scale',
  PERCENTAGE: 'percent',
  DOLLAR: 'dollar-sign',

  // Investment & Finance
  INVEST: 'piggy-bank',
  DEPOSIT: 'hand-holding-usd',
  WITHDRAW: 'money-bill-wave',
  BANK: 'landmark',
  CREDIT_CARD: 'credit-card',
  RECEIPT: 'receipt',
  CALCULATOR: 'calculator',

  // Actions
  PLAY: 'play',
  PAUSE: 'pause',
  STOP: 'stop',
  SEARCH: 'search',
  FILTER: 'filter',
  SORT: 'sort',
  PLUS: 'plus',
  MINUS: 'minus',
  EDIT: 'edit',
  DELETE: 'trash',
  SAVE: 'save',
  REFRESH: 'refresh',
  SYNC: 'sync',

  // Status & Indicators
  SUCCESS: 'check-circle',
  ERROR: 'times-circle',
  WARNING: 'exclamation-triangle',
  INFO: 'info-circle',
  LOADING: 'spinner',
  ACTIVE: 'circle',
  INACTIVE: 'circle',
  STAR: 'star',
  HEART: 'heart',

  // Security & Trust
  SHIELD: 'shield',
  CERTIFICATE: 'certificate',
  AWARD: 'award',
  KEY: 'key',

  // Communication
  EMAIL: 'envelope',
  PHONE: 'phone',
  NOTIFICATION: 'bell',
  BELL: 'bell',
  CHAT: 'comment',
  COMMENTS: 'comments',
  SEND: 'paper-plane',

  // Data & Analytics
  CHART_BAR: 'chart-bar',
  CHART_PIE: 'chart-pie',
  TABLE: 'table',
  DATABASE: 'database',
  ANALYTICS: 'chart-bar',
  EXPORT: 'file-export',
  IMPORT: 'file-import',

  // Social & External
  EXTERNAL_LINK: 'external-link-alt',
  SHARE: 'share',
  COPY: 'copy',
  LINK: 'link',
  GITHUB: 'github',
  TWITTER: 'twitter',
  DISCORD: 'discord',

  // Files & Folders
  FILE: 'file-alt',
  FOLDER: 'folder',
  DOWNLOAD: 'download',
  UPLOAD: 'upload',

  // Time & Calendar
  CLOCK: 'clock',
  CALENDAR: 'calendar',

  // Misc
  ROCKET: 'rocket',
  GEM: 'gem',
  FIRE: 'fire',
  LIGHTNING: 'bolt',
  MAGIC: 'magic',
  GIFT: 'gift',
  QR_CODE: 'qrcode',
  GLOBE: 'globe',
  BUILDING: 'building',
  INDUSTRY: 'industry',
  CUBE: 'cube',
  LAYERS: 'layer-group',
  NETWORK: 'network-wired',

  // Aliases for common usage
  ALERT: 'exclamation-triangle',
  QUESTION: 'question-circle',
  HELP: 'question-circle',
} as const;
