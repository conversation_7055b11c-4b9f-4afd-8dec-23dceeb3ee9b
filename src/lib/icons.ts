/**
 * ==============================================
 * FONTAWESOME ICON CONFIGURATION
 * ==============================================
 * 
 * This file configures FontAwesome icons for the crypto trading platform.
 * Replaces Lucide React icons with FontAwesome for better crypto-themed icons.
 */

import { library } from '@fortawesome/fontawesome-svg-core';
import {
  // Navigation and UI
  faHome,
  faRobot,
  faWallet,
  faChartLine,
  faCog,
  faSignOutAlt,
  faBars,
  faTimes,
  faUser,
  faLock,
  faEye,
  faEyeSlash,
  
  // Crypto and Trading
  faCoins,
  faArrowUp,
  faArrowDown,
  faArrowRight,
  faArrowLeft,
  faExchangeAlt,
  faBalanceScale,
  
  // Actions and Features
  faPlay,
  faPause,
  faStop,
  faDownload,
  faUpload,
  faSearch,
  faFilter,
  faSort,
  faPlus,
  faMinus,
  faEdit,
  faTrash,
  faSave,
  faCancel,
  
  // Status and Indicators
  faCheckCircle,
  faTimesCircle,
  faExclamationTriangle,
  faInfoCircle,
  faSpinner,
  faClock,
  faCalendar,
  faStar,
  faHeart,
  
  // Security and Trust
  faShield,
  faLockOpen,
  faKey,
  faCertificate,
  faAward,
  
  // Communication
  faEnvelope,
  faPhone,
  faBell,
  faComment,
  faQuestionCircle,
  
  // Data and Analytics
  faChartBar,
  faChartPie,
  faTable,
  faDatabase,
  faFileExport,
  faFileImport,
  
  // Social and External
  faExternalLinkAlt,
  faShare,
  faCopy,
  faLink,
  
  // Misc
  faGlobe,
  faMapMarker,
  faBuilding,
  faIndustry,
  faRocket,
  faGem,
  faFire,
  faBolt,
  faMagic,
  faGift,
  faQrcode,
  faPaperPlane,
  faFileAlt,
  faFolder,
} from '@fortawesome/free-solid-svg-icons';

// Add icons to the library
library.add(
  // Navigation and UI
  faHome,
  faRobot,
  faWallet,
  faChartLine,
  faCog,
  faSignOutAlt,
  faBars,
  faTimes,
  faUser,
  faLock,
  faEye,
  faEyeSlash,
  
  // Crypto and Trading
  faCoins,
  faArrowUp,
  faArrowDown,
  faArrowRight,
  faArrowLeft,
  faExchangeAlt,
  faBalanceScale,
  
  // Actions and Features
  faPlay,
  faPause,
  faStop,
  faDownload,
  faUpload,
  faSearch,
  faFilter,
  faSort,
  faPlus,
  faMinus,
  faEdit,
  faTrash,
  faSave,
  faCancel,
  
  // Status and Indicators
  faCheckCircle,
  faTimesCircle,
  faExclamationTriangle,
  faInfoCircle,
  faSpinner,
  faClock,
  faCalendar,
  faStar,
  faHeart,
  
  // Security and Trust
  faShield,
  faLockOpen,
  faKey,
  faCertificate,
  faAward,
  
  // Communication
  faEnvelope,
  faPhone,
  faBell,
  faComment,
  faQuestionCircle,
  
  // Data and Analytics
  faChartBar,
  faChartPie,
  faTable,
  faDatabase,
  faFileExport,
  faFileImport,
  
  // Social and External
  faExternalLinkAlt,
  faShare,
  faCopy,
  faLink,
  
  // Misc
  faGlobe,
  faMapMarker,
  faBuilding,
  faIndustry,
  faRocket,
  faGem,
  faFire,
  faBolt,
  faMagic,
  faGift,
  faQrcode,
  faPaperPlane,
  faFileAlt,
  faFolder,
);

// Export commonly used icon names for easy reference
export const ICON_NAMES = {
  // Navigation
  HOME: 'home',
  ROBOT: 'robot',
  WALLET: 'wallet',
  CHART: 'chart-line',
  SETTINGS: 'cog',
  LOGOUT: 'sign-out-alt',
  MENU: 'bars',
  CLOSE: 'times',
  
  // User
  USER: 'user',
  LOCK: 'lock',
  EYE: 'eye',
  EYE_SLASH: 'eye-slash',
  
  // Crypto
  BITCOIN: 'coins', // Using coins as alternative
  ETHEREUM: 'coins', // Using coins as alternative
  COINS: 'coins',
  TRENDING_UP: 'arrow-up', // Using arrow-up as alternative
  TRENDING_DOWN: 'arrow-down', // Using arrow-down as alternative
  EXCHANGE: 'exchange-alt',
  
  // Actions
  PLAY: 'play',
  PAUSE: 'pause',
  STOP: 'stop',
  SEARCH: 'search',
  FILTER: 'filter',
  PLUS: 'plus',
  EDIT: 'edit',
  DELETE: 'trash',
  
  // Status
  SUCCESS: 'check-circle',
  ERROR: 'times-circle',
  WARNING: 'exclamation-triangle',
  INFO: 'info-circle',
  LOADING: 'spinner',
  
  // Security
  SHIELD: 'shield',
  CERTIFICATE: 'certificate',
  AWARD: 'award',
  
  // Communication
  EMAIL: 'envelope',
  PHONE: 'phone',
  NOTIFICATION: 'bell',
  
  // Misc
  ROCKET: 'rocket',
  GEM: 'gem',
  FIRE: 'fire',
  LIGHTNING: 'bolt', // Using bolt instead of lightning
  MAGIC: 'magic',
  GIFT: 'gift',
  KEY: 'key',

  // Communication
  CHAT: 'comment',
  BELL: 'bell',
  SEND: 'paper-plane',

  // Files
  FILE: 'file-alt',

  // Additional icons
  ALERT: 'exclamation-triangle',
  QR_CODE: 'qrcode',
  COPY: 'copy',
  FOLDER: 'folder',
  DOWNLOAD: 'download',
  UPLOAD: 'upload',
  CLOCK: 'clock',
} as const;
