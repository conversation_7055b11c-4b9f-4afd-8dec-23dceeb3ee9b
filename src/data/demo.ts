/**
 * ==============================================
 * DEMO ACCOUNT DATA
 * ==============================================
 * 
 * This file contains demo account credentials and sample data
 * for testing and demonstration purposes.
 */

import { User, Portfolio, TradingBot, BotInvestment, Trade, WalletTransaction } from '../types';

// ==============================================
// DEMO ACCOUNT CREDENTIALS
// ==============================================

export const DEMO_ACCOUNTS = {
  user: {
    username: 'demo',
    password: 'demo123',
    email: '<EMAIL>'
  },
  admin: {
    username: 'admin',
    password: 'admin123',
    email: '<EMAIL>'
  }
} as const;

// ==============================================
// DEMO USER DATA
// ==============================================

export const DEMO_USER: User = {
  id: 'demo-user-001',
  email: '<EMAIL>',
  username: 'demo',
  name: 'Alex Thompson',
  wallet_address: '0x742d35Cc6634C0532925a3b8D4C9db96590b5',
  avatar_url: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
  is_verified: true,
  kyc_status: 'approved',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-15T00:00:00Z',
  preferences: {
    risk_tolerance: 'medium',
    notifications: {
      email: true,
      push: true,
      trade_alerts: true,
      profit_alerts: true,
      loss_alerts: false,
    },
    theme: 'dark',
    currency: 'USD',
  },
};

// ==============================================
// DEMO TRADING BOTS
// ==============================================

export const DEMO_TRADING_BOTS: TradingBot[] = [
  {
    id: 'bot-moonshiba-001',
    name: 'MoonShiba Pro',
    description: 'Advanced momentum trading bot specialized in Shiba Inu and similar meme coins. Uses machine learning to identify breakout patterns and execute trades with precision timing.',
    strategy: {
      type: 'momentum',
      parameters: {
        momentum_threshold: 0.05,
        volume_threshold: 1000000,
        rsi_oversold: 30,
        rsi_overbought: 70,
      },
      stop_loss_percentage: 5,
      take_profit_percentage: 15,
      max_trades_per_day: 10,
    },
    target_coins: ['SHIB', 'DOGE', 'PEPE', 'FLOKI'],
    risk_level: 'medium',
    min_investment: 100,
    max_investment: 10000,
    performance: {
      total_return_percentage: 24.5,
      win_rate: 68.2,
      total_trades: 156,
      profitable_trades: 106,
      average_trade_duration: 4.2,
      max_drawdown: 8.3,
      sharpe_ratio: 1.8,
      last_30_days_return: 12.3,
      last_7_days_return: 3.7,
      historical_data: [],
    },
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-15T00:00:00Z',
  },
  {
    id: 'bot-arbitrage-master-002',
    name: 'Arbitrage Master',
    description: 'Cross-exchange arbitrage bot that identifies price differences across multiple exchanges and executes profitable trades with minimal risk exposure.',
    strategy: {
      type: 'arbitrage',
      parameters: {
        min_spread: 0.02,
        max_slippage: 0.01,
        exchange_fees: 0.001,
      },
      stop_loss_percentage: 2,
      take_profit_percentage: 5,
      max_trades_per_day: 50,
    },
    target_coins: ['DOGE', 'SHIB', 'FLOKI', 'BONK'],
    risk_level: 'low',
    min_investment: 500,
    max_investment: 50000,
    performance: {
      total_return_percentage: 18.2,
      win_rate: 89.1,
      total_trades: 342,
      profitable_trades: 304,
      average_trade_duration: 0.8,
      max_drawdown: 3.1,
      sharpe_ratio: 2.4,
      last_30_days_return: 8.9,
      last_7_days_return: 2.1,
      historical_data: [],
    },
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-15T00:00:00Z',
  },
  {
    id: 'bot-dca-diamond-003',
    name: 'DCA Diamond Hands',
    description: 'Dollar-cost averaging bot that systematically accumulates meme coins during market dips and sells during peaks using advanced technical indicators.',
    strategy: {
      type: 'dca',
      parameters: {
        buy_interval_hours: 24,
        dip_threshold: -0.05,
        accumulation_period: 30,
      },
      stop_loss_percentage: 15,
      take_profit_percentage: 25,
      max_trades_per_day: 5,
    },
    target_coins: ['PEPE', 'WIF', 'BONK', 'MEME'],
    risk_level: 'high',
    min_investment: 200,
    max_investment: 25000,
    performance: {
      total_return_percentage: 45.7,
      win_rate: 72.5,
      total_trades: 89,
      profitable_trades: 64,
      average_trade_duration: 12.5,
      max_drawdown: 18.2,
      sharpe_ratio: 1.6,
      last_30_days_return: 22.1,
      last_7_days_return: 8.3,
      historical_data: [],
    },
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-15T00:00:00Z',
  },
];

// ==============================================
// DEMO PORTFOLIO
// ==============================================

export const DEMO_PORTFOLIO: Portfolio = {
  id: 'portfolio-demo-001',
  user_id: 'demo-user-001',
  total_value: 7420.50,
  total_invested: 6000.00,
  total_profit_loss: 1420.50,
  profit_loss_percentage: 23.67,
  active_bots: [],
  wallet_balance: {
    usd: 1200.00,
    eth: 0.8542,
    btc: 0.0234,
  },
  created_at: '2024-01-01T00:00:00Z',
  updated_at: new Date().toISOString(),
};

// ==============================================
// DEMO BOT INVESTMENTS
// ==============================================

export const DEMO_BOT_INVESTMENTS: BotInvestment[] = [
  {
    id: 'investment-001',
    user_id: 'demo-user-001',
    bot_id: 'bot-moonshiba-001',
    bot: DEMO_TRADING_BOTS[0],
    invested_amount: 2500,
    current_value: 3112.50,
    profit_loss: 612.50,
    profit_loss_percentage: 24.5,
    status: 'active',
    start_date: '2024-01-10T00:00:00Z',
    last_trade_date: '2024-01-15T14:30:00Z',
    trades: [],
  },
  {
    id: 'investment-002',
    user_id: 'demo-user-001',
    bot_id: 'bot-arbitrage-master-002',
    bot: DEMO_TRADING_BOTS[1],
    invested_amount: 2000,
    current_value: 2364.00,
    profit_loss: 364.00,
    profit_loss_percentage: 18.2,
    status: 'active',
    start_date: '2024-01-12T00:00:00Z',
    last_trade_date: '2024-01-15T16:45:00Z',
    trades: [],
  },
  {
    id: 'investment-003',
    user_id: 'demo-user-001',
    bot_id: 'bot-dca-diamond-003',
    bot: DEMO_TRADING_BOTS[2],
    invested_amount: 1500,
    current_value: 1944.00,
    profit_loss: 444.00,
    profit_loss_percentage: 29.6,
    status: 'active',
    start_date: '2024-01-08T00:00:00Z',
    last_trade_date: '2024-01-15T12:20:00Z',
    trades: [],
  },
];

// ==============================================
// DEMO TRADES
// ==============================================

export const DEMO_TRADES: Trade[] = [
  {
    id: 'trade-001',
    bot_investment_id: 'investment-001',
    coin_symbol: 'SHIB',
    type: 'buy',
    amount: 2500000,
    price: 0.00001245,
    total_value: 31.125,
    fees: 0.15,
    status: 'completed',
    executed_at: '2024-01-15T14:30:00Z',
    transaction_hash: '0x1234567890abcdef1234567890abcdef12345678',
  },
  {
    id: 'trade-002',
    bot_investment_id: 'investment-001',
    coin_symbol: 'SHIB',
    type: 'sell',
    amount: 1200000,
    price: 0.00001398,
    total_value: 16.776,
    fees: 0.08,
    profit_loss: 1.84,
    status: 'completed',
    executed_at: '2024-01-15T16:15:00Z',
    transaction_hash: '******************************************',
  },
  {
    id: 'trade-003',
    bot_investment_id: 'investment-002',
    coin_symbol: 'DOGE',
    type: 'buy',
    amount: 1500,
    price: 0.08234,
    total_value: 123.51,
    fees: 0.62,
    status: 'completed',
    executed_at: '2024-01-15T11:20:00Z',
    transaction_hash: '******************************************',
  },
];

// ==============================================
// DEMO WALLET TRANSACTIONS
// ==============================================

export const DEMO_WALLET_TRANSACTIONS: WalletTransaction[] = [
  {
    id: 'tx-001',
    user_id: 'demo-user-001',
    type: 'deposit',
    currency: 'ETH',
    amount: 1.0,
    status: 'completed',
    transaction_hash: '******************************************',
    fees: 0.002,
    created_at: '2024-01-10T09:30:00Z',
    completed_at: '2024-01-10T09:35:00Z',
  },
  {
    id: 'tx-002',
    user_id: 'demo-user-001',
    type: 'deposit',
    currency: 'USDT',
    amount: 5000,
    status: 'completed',
    transaction_hash: '0xaaabbbbccccddddeeeeffffgggghhhhiiiijjjj',
    fees: 5.0,
    created_at: '2024-01-08T15:20:00Z',
    completed_at: '2024-01-08T15:25:00Z',
  },
  {
    id: 'tx-003',
    user_id: 'demo-user-001',
    type: 'withdraw',
    currency: 'BTC',
    amount: 0.01,
    status: 'pending',
    wallet_address: '**********************************',
    fees: 0.0005,
    created_at: '2024-01-15T18:45:00Z',
  },
];

// ==============================================
// APP CONFIGURATION
// ==============================================

export const APP_CONFIG = {
  name: 'MemeBot Pro',
  version: '1.0.0',
  description: 'Advanced crypto meme coin trading platform with AI-powered bots',
  support_email: '<EMAIL>',
  website: 'https://memebot.pro',
  social: {
    twitter: '@MemeBot_Pro',
    discord: 'https://discord.gg/memebot',
    telegram: 'https://t.me/memebot_pro',
  },
  features: [
    'AI-Powered Trading Bots',
    '24/7 Automated Trading',
    'Multi-Exchange Arbitrage',
    'Advanced Risk Management',
    'Real-time Portfolio Tracking',
    'Secure Wallet Integration',
  ],
  supported_exchanges: [
    'Binance',
    'Coinbase Pro',
    'KuCoin',
    'Gate.io',
    'Uniswap',
    'PancakeSwap',
  ],
  supported_coins: [
    'DOGE', 'SHIB', 'PEPE', 'FLOKI', 'BONK', 'WIF', 'MEME', 'WOJAK'
  ],
} as const;
