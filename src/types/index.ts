/**
 * ==============================================
 * CRYPTO TRADING PLATFORM TYPE DEFINITIONS
 * ==============================================
 * 
 * This file contains all TypeScript interfaces and types for the
 * memecoin trading bot platform. These types define the structure
 * of data used throughout the application.
 */

// ==============================================
// USER MANAGEMENT TYPES
// ==============================================

/**
 * User interface representing a platform user
 * Supports both email and wallet-based authentication
 */
export interface User {
  id: string;
  email?: string;
  username: string;
  name?: string;
  wallet_address?: string;
  avatar_url?: string;
  is_verified: boolean;
  kyc_status: 'pending' | 'approved' | 'rejected' | 'not_required';
  created_at: string;
  updated_at: string;
  preferences: UserPreferences;
}

/**
 * User preferences for trading and notifications
 */
export interface UserPreferences {
  risk_tolerance: 'low' | 'medium' | 'high';
  notifications: {
    email: boolean;
    push: boolean;
    trade_alerts: boolean;
    profit_alerts: boolean;
    loss_alerts: boolean;
  };
  theme: 'light' | 'dark';
  currency: 'USD' | 'ETH' | 'BTC';
}

// ==============================================
// CRYPTOCURRENCY TYPES
// ==============================================

/**
 * Memecoin interface representing supported cryptocurrencies
 */
export interface MemeCoin {
  id: string;
  symbol: string;
  name: string;
  logo_url: string;
  current_price: number;
  market_cap: number;
  volume_24h: number;
  price_change_24h: number;
  price_change_percentage_24h: number;
  contract_address: string;
  blockchain: 'ethereum' | 'bsc' | 'polygon' | 'solana';
  is_active: boolean;
  created_at: string;
}

/**
 * Price data for charts and historical analysis
 */
export interface PriceData {
  timestamp: number;
  price: number;
  volume: number;
}

// ==============================================
// TRADING BOT TYPES
// ==============================================

/**
 * Trading bot interface representing automated trading strategies
 */
export interface TradingBot {
  id: string;
  name: string;
  description: string;
  strategy: BotStrategy;
  target_coins: string[]; // Array of memecoin symbols
  risk_level: 'low' | 'medium' | 'high';
  min_investment: number;
  max_investment: number;
  performance: BotPerformance;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Bot strategy configuration
 */
export interface BotStrategy {
  type: 'arbitrage' | 'momentum' | 'mean_reversion' | 'grid' | 'dca';
  parameters: {
    [key: string]: any;
  };
  stop_loss_percentage: number;
  take_profit_percentage: number;
  max_trades_per_day: number;
}

/**
 * Bot performance metrics
 */
export interface BotPerformance {
  total_return_percentage: number;
  win_rate: number;
  total_trades: number;
  profitable_trades: number;
  average_trade_duration: number; // in hours
  max_drawdown: number;
  sharpe_ratio: number;
  last_30_days_return: number;
  last_7_days_return: number;
  historical_data: PriceData[];
}

// ==============================================
// PORTFOLIO AND INVESTMENT TYPES
// ==============================================

/**
 * User portfolio containing all investments and balances
 */
export interface Portfolio {
  id: string;
  user_id: string;
  total_value: number;
  total_invested: number;
  total_profit_loss: number;
  profit_loss_percentage: number;
  active_bots: BotInvestment[];
  wallet_balance: WalletBalance;
  created_at: string;
  updated_at: string;
}

/**
 * Individual bot investment by a user
 */
export interface BotInvestment {
  id: string;
  user_id: string;
  bot_id: string;
  bot: TradingBot;
  invested_amount: number;
  current_value: number;
  profit_loss: number;
  profit_loss_percentage: number;
  status: 'active' | 'paused' | 'stopped';
  start_date: string;
  last_trade_date?: string;
  trades: Trade[];
}

/**
 * User wallet balance across different cryptocurrencies
 */
export interface WalletBalance {
  usd: number;
  eth: number;
  btc: number;
  [key: string]: number; // For other cryptocurrencies
}

// ==============================================
// TRADING TYPES
// ==============================================

/**
 * Individual trade executed by a bot
 */
export interface Trade {
  id: string;
  bot_investment_id: string;
  coin_symbol: string;
  type: 'buy' | 'sell';
  amount: number;
  price: number;
  total_value: number;
  fees: number;
  profit_loss?: number;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  executed_at: string;
  transaction_hash?: string;
}

/**
 * Trade history with pagination support
 */
export interface TradeHistory {
  trades: Trade[];
  total_count: number;
  page: number;
  per_page: number;
  total_pages: number;
}

// ==============================================
// WALLET AND TRANSACTION TYPES
// ==============================================

/**
 * Wallet transaction for deposits and withdrawals
 */
export interface WalletTransaction {
  id: string;
  user_id: string;
  type: 'deposit' | 'withdrawal';
  currency: string;
  amount: number;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  transaction_hash?: string;
  wallet_address?: string;
  fees: number;
  created_at: string;
  completed_at?: string;
}

/**
 * Supported wallet providers
 */
export interface WalletProvider {
  id: string;
  name: string;
  icon: string;
  is_supported: boolean;
}

// ==============================================
// API RESPONSE TYPES
// ==============================================

/**
 * Standard API response wrapper
 */
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

/**
 * Paginated response for lists
 */
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    per_page: number;
    total: number;
    total_pages: number;
  };
}

// ==============================================
// UTILITY TYPES
// ==============================================

/**
 * Chart data for performance visualization
 */
export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    borderColor: string;
    backgroundColor: string;
    fill?: boolean;
  }[];
}

/**
 * Filter options for various list views
 */
export interface FilterOptions {
  search?: string;
  risk_level?: 'low' | 'medium' | 'high';
  strategy_type?: string;
  min_return?: number;
  max_return?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

// ==============================================
// LEGACY TYPES (TO BE REMOVED)
// ==============================================
// These types are kept temporarily for compatibility
// during the migration from banking to crypto platform

export interface Account {
  id: string;
  name: string;
  type: string;
  balance: number;
}

export interface Transaction {
  id: string;
  amount: number;
  type: 'credit' | 'debit';
  description: string;
  date: string;
  category?: string;
}

export interface Transfer {
  id: string;
  fromAccount: string;
  toAccount: string;
  amount: number;
  date: string;
  status: 'pending' | 'completed' | 'failed';
  description: string;
  type: 'internal' | 'external';
}

export interface TransferResult {
  success: boolean;
  error?: string;
}

export interface AdminSession {
  user_id: string;
  username: string;
  role: string;
}
