/**
 * ==============================================
 * THEME TOGGLE COMPONENT
 * ==============================================
 * 
 * Beautiful theme toggle button with smooth animations
 * and visual feedback for light/dark/system modes.
 */

import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';
import { useTheme, themeClass } from '../lib/ThemeContext';

interface ThemeToggleProps {
  className?: string;
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className = '',
  showLabel = false,
  size = 'md',
}) => {
  const { theme, actualTheme, toggleTheme } = useTheme();

  const getIcon = () => {
    switch (theme) {
      case 'light':
        return ICON_NAMES.SUN;
      case 'dark':
        return ICON_NAMES.MOON;
      case 'system':
        return ICON_NAMES.DESKTOP;
      default:
        return ICON_NAMES.SUN;
    }
  };

  const getLabel = () => {
    switch (theme) {
      case 'light':
        return 'Light Mode';
      case 'dark':
        return 'Dark Mode';
      case 'system':
        return 'System';
      default:
        return 'Light Mode';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-8 h-8 text-sm';
      case 'md':
        return 'w-10 h-10 text-base';
      case 'lg':
        return 'w-12 h-12 text-lg';
      default:
        return 'w-10 h-10 text-base';
    }
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <button
        onClick={toggleTheme}
        className={`
          ${getSizeClasses()}
          ${themeClass.bgSecondary}
          ${themeClass.textPrimary}
          ${themeClass.borderPrimary}
          ${themeClass.hoverBg}
          border rounded-xl
          flex items-center justify-center
          transition-all duration-300 ease-in-out
          transform hover:scale-105
          focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2
          relative overflow-hidden
          group
        `}
        title={`Switch to ${theme === 'light' ? 'dark' : theme === 'dark' ? 'system' : 'light'} mode`}
      >
        {/* Background animation */}
        <div className={`
          absolute inset-0 
          ${actualTheme === 'dark' 
            ? 'bg-gradient-to-br from-purple-600 to-blue-600' 
            : 'bg-gradient-to-br from-yellow-400 to-orange-500'
          }
          opacity-0 group-hover:opacity-20 
          transition-opacity duration-300
        `} />
        
        {/* Icon with rotation animation */}
        <FontAwesomeIcon 
          icon={getIcon()} 
          className={`
            transition-all duration-500 ease-in-out
            ${theme === 'system' ? 'animate-pulse' : ''}
            ${actualTheme === 'dark' ? 'text-blue-400' : 'text-yellow-600'}
            relative z-10
          `}
        />
        
        {/* Subtle glow effect */}
        <div className={`
          absolute inset-0 rounded-xl
          ${actualTheme === 'dark' 
            ? 'shadow-blue-500/20' 
            : 'shadow-yellow-500/20'
          }
          shadow-lg opacity-0 group-hover:opacity-100
          transition-opacity duration-300
        `} />
      </button>

      {showLabel && (
        <span className={`
          ${themeClass.textSecondary}
          text-sm font-medium
          transition-colors duration-300
        `}>
          {getLabel()}
        </span>
      )}
    </div>
  );
};

// Compact theme toggle for mobile/small spaces
export const CompactThemeToggle: React.FC<{ className?: string }> = ({ className = '' }) => {
  const { theme, actualTheme, toggleTheme } = useTheme();

  return (
    <button
      onClick={toggleTheme}
      className={`
        w-8 h-8 rounded-lg
        ${themeClass.bgSecondary}
        ${themeClass.textSecondary}
        ${themeClass.hoverBg}
        ${themeClass.hoverText}
        flex items-center justify-center
        transition-all duration-200
        ${className}
      `}
      title="Toggle theme"
    >
      <FontAwesomeIcon 
        icon={theme === 'light' ? ICON_NAMES.SUN : theme === 'dark' ? ICON_NAMES.MOON : ICON_NAMES.DESKTOP} 
        className={`text-sm ${actualTheme === 'dark' ? 'text-blue-400' : 'text-yellow-600'}`}
      />
    </button>
  );
};

// Theme selector dropdown
export const ThemeSelector: React.FC<{ className?: string }> = ({ className = '' }) => {
  const { theme, setTheme } = useTheme();
  const [isOpen, setIsOpen] = React.useState(false);

  const themes = [
    { value: 'light', label: 'Light', icon: ICON_NAMES.SUN },
    { value: 'dark', label: 'Dark', icon: ICON_NAMES.MOON },
    { value: 'system', label: 'System', icon: ICON_NAMES.DESKTOP },
  ] as const;

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`
          ${themeClass.bgSecondary}
          ${themeClass.textPrimary}
          ${themeClass.borderPrimary}
          ${themeClass.hoverBg}
          border rounded-xl px-4 py-2
          flex items-center space-x-2
          transition-colors duration-200
          focus:outline-none focus:ring-2 focus:ring-purple-500
        `}
      >
        <FontAwesomeIcon 
          icon={themes.find(t => t.value === theme)?.icon || ICON_NAMES.SUN} 
          className="text-sm"
        />
        <span className="text-sm font-medium">
          {themes.find(t => t.value === theme)?.label}
        </span>
        <FontAwesomeIcon 
          icon={ICON_NAMES.CHEVRON_DOWN} 
          className={`text-xs transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
        />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className={`
            absolute top-full left-0 mt-2 w-full
            ${themeClass.bgCard}
            ${themeClass.borderPrimary}
            border rounded-xl shadow-lg
            z-20
            overflow-hidden
          `}>
            {themes.map((themeOption) => (
              <button
                key={themeOption.value}
                onClick={() => {
                  setTheme(themeOption.value);
                  setIsOpen(false);
                }}
                className={`
                  w-full px-4 py-3 text-left
                  ${themeClass.textPrimary}
                  ${themeClass.hoverBg}
                  flex items-center space-x-3
                  transition-colors duration-200
                  ${theme === themeOption.value ? 'bg-purple-50 dark:bg-purple-900/20' : ''}
                `}
              >
                <FontAwesomeIcon 
                  icon={themeOption.icon} 
                  className={`text-sm ${theme === themeOption.value ? 'text-purple-600 dark:text-purple-400' : ''}`}
                />
                <span className={`text-sm font-medium ${theme === themeOption.value ? 'text-purple-600 dark:text-purple-400' : ''}`}>
                  {themeOption.label}
                </span>
                {theme === themeOption.value && (
                  <FontAwesomeIcon 
                    icon={ICON_NAMES.CHECK} 
                    className="text-sm text-purple-600 dark:text-purple-400 ml-auto"
                  />
                )}
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  );
};
