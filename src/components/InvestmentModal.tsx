/**
 * ==============================================
 * INVESTMENT MODAL COMPONENT
 * ==============================================
 * 
 * Modal for investing in trading bots with amount selection,
 * risk acknowledgment, and investment confirmation.
 */

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';
import { colors, components, animations } from '../lib/designSystem';
import { formatCurrency, formatPercentage } from '../lib/utils';

interface InvestmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (amount: number, autoReinvest: boolean) => void;
  bot: {
    id: string;
    name: string;
    riskLevel: 'low' | 'medium' | 'high';
    minInvestment: number;
    maxInvestment: number;
    expectedReturn: number;
    winRate: number;
    description: string;
  } | null;
  userBalance: number;
}

export const InvestmentModal: React.FC<InvestmentModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  bot,
  userBalance,
}) => {
  const [amount, setAmount] = useState(0);
  const [autoReinvest, setAutoReinvest] = useState(true);
  const [step, setStep] = useState(1); // 1: Amount, 2: Confirmation
  const [agreedToRisks, setAgreedToRisks] = useState(false);

  if (!isOpen || !bot) return null;

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-blue-600 bg-blue-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getRiskGradient = (risk: string) => {
    switch (risk) {
      case 'low': return 'from-green-500 to-emerald-500';
      case 'medium': return 'from-blue-500 to-cyan-500';
      case 'high': return 'from-red-500 to-pink-500';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  const handleAmountChange = (value: number) => {
    setAmount(Math.max(bot.minInvestment, Math.min(value, Math.min(bot.maxInvestment, userBalance))));
  };

  const quickAmounts = [
    bot.minInvestment,
    Math.min(500, bot.maxInvestment),
    Math.min(1000, bot.maxInvestment),
    Math.min(5000, bot.maxInvestment),
  ].filter((val, index, arr) => arr.indexOf(val) === index);

  const projectedReturn = (amount * bot.expectedReturn) / 100;
  const isValidAmount = amount >= bot.minInvestment && amount <= bot.maxInvestment && amount <= userBalance;

  const handleConfirm = () => {
    if (step === 1 && isValidAmount) {
      setStep(2);
    } else if (step === 2 && agreedToRisks) {
      onConfirm(amount, autoReinvest);
      onClose();
      setStep(1);
      setAmount(0);
      setAgreedToRisks(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
      <div className="bg-white rounded-3xl w-full max-w-lg shadow-2xl animate-scale-in max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-100">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Invest in Bot</h2>
            <p className="text-sm text-gray-600">Step {step} of 2</p>
          </div>
          <button
            onClick={onClose}
            className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
          >
            <FontAwesomeIcon icon={ICON_NAMES.CLOSE} className="text-gray-500" />
          </button>
        </div>

        {step === 1 && (
          <div className="p-6 space-y-6">
            {/* Bot Info */}
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-4 border border-purple-100">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-lg font-bold text-gray-900">{bot.name}</h3>
                <span className={`px-2 py-1 rounded-lg text-xs font-medium ${getRiskColor(bot.riskLevel)}`}>
                  {bot.riskLevel.toUpperCase()}
                </span>
              </div>
              <p className="text-sm text-gray-600 mb-3">{bot.description}</p>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Expected Return:</span>
                  <div className="font-bold text-green-600">+{formatPercentage(bot.expectedReturn)}</div>
                </div>
                <div>
                  <span className="text-gray-600">Win Rate:</span>
                  <div className="font-bold text-gray-900">{formatPercentage(bot.winRate)}</div>
                </div>
              </div>
            </div>

            {/* Amount Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Investment Amount
              </label>
              
              {/* Amount Input */}
              <div className="relative mb-4">
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</div>
                <input
                  type="number"
                  value={amount || ''}
                  onChange={(e) => handleAmountChange(Number(e.target.value))}
                  placeholder="Enter amount"
                  className="w-full pl-8 pr-4 py-3 rounded-xl border border-gray-200 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 text-lg font-medium"
                />
              </div>

              {/* Quick Amount Buttons */}
              <div className="grid grid-cols-2 gap-2 mb-4">
                {quickAmounts.map((quickAmount) => (
                  <button
                    key={quickAmount}
                    onClick={() => handleAmountChange(quickAmount)}
                    className="py-2 px-4 rounded-xl border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-colors text-sm font-medium"
                  >
                    {formatCurrency(quickAmount)}
                  </button>
                ))}
              </div>

              {/* Amount Slider */}
              <div className="mb-4">
                <input
                  type="range"
                  min={bot.minInvestment}
                  max={Math.min(bot.maxInvestment, userBalance)}
                  value={amount}
                  onChange={(e) => handleAmountChange(Number(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>{formatCurrency(bot.minInvestment)}</span>
                  <span>{formatCurrency(Math.min(bot.maxInvestment, userBalance))}</span>
                </div>
              </div>

              {/* Balance Info */}
              <div className="flex justify-between text-sm text-gray-600 mb-4">
                <span>Available Balance:</span>
                <span className="font-medium">{formatCurrency(userBalance)}</span>
              </div>
            </div>

            {/* Projected Returns */}
            {amount > 0 && (
              <div className="bg-green-50 rounded-2xl p-4 border border-green-100">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Projected Annual Return</h4>
                <div className="text-2xl font-bold text-green-600 mb-1">
                  +{formatCurrency(projectedReturn)}
                </div>
                <div className="text-xs text-gray-600">
                  Based on {formatPercentage(bot.expectedReturn)} expected return
                </div>
              </div>
            )}

            {/* Auto-Reinvest Option */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-2xl">
              <div>
                <div className="text-sm font-medium text-gray-900">Auto-Reinvest Profits</div>
                <div className="text-xs text-gray-600">Automatically reinvest profits for compound growth</div>
              </div>
              <button
                onClick={() => setAutoReinvest(!autoReinvest)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  autoReinvest ? 'bg-purple-500' : 'bg-gray-300'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    autoReinvest ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            {/* Continue Button */}
            <button
              onClick={handleConfirm}
              disabled={!isValidAmount}
              className={`w-full py-3 rounded-2xl font-semibold text-white transition-all duration-200 ${
                isValidAmount
                  ? `bg-gradient-to-r ${getRiskGradient(bot.riskLevel)} hover:shadow-lg transform hover:scale-105`
                  : 'bg-gray-300 cursor-not-allowed'
              }`}
            >
              Continue to Confirmation
            </button>
          </div>
        )}

        {step === 2 && (
          <div className="p-6 space-y-6">
            {/* Investment Summary */}
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-4 border border-purple-100">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Investment Summary</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Bot:</span>
                  <span className="font-medium">{bot.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Investment Amount:</span>
                  <span className="font-bold text-lg">{formatCurrency(amount)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Expected Annual Return:</span>
                  <span className="font-medium text-green-600">+{formatCurrency(projectedReturn)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Auto-Reinvest:</span>
                  <span className="font-medium">{autoReinvest ? 'Enabled' : 'Disabled'}</span>
                </div>
              </div>
            </div>

            {/* Risk Acknowledgment */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-900">Risk Acknowledgment</h4>
              <div className="space-y-3 text-xs text-gray-600">
                <div className="flex items-start space-x-2">
                  <FontAwesomeIcon icon={ICON_NAMES.WARNING} className="text-yellow-500 mt-0.5" />
                  <span>Cryptocurrency trading involves substantial risk and may result in loss of capital.</span>
                </div>
                <div className="flex items-start space-x-2">
                  <FontAwesomeIcon icon={ICON_NAMES.INFO} className="text-blue-500 mt-0.5" />
                  <span>Past performance does not guarantee future results.</span>
                </div>
                <div className="flex items-start space-x-2">
                  <FontAwesomeIcon icon={ICON_NAMES.SHIELD} className="text-green-500 mt-0.5" />
                  <span>Your investment is protected by our security measures and insurance.</span>
                </div>
              </div>

              <label className="flex items-start space-x-3 cursor-pointer">
                <input
                  type="checkbox"
                  checked={agreedToRisks}
                  onChange={(e) => setAgreedToRisks(e.target.checked)}
                  className="mt-1 w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                />
                <span className="text-sm text-gray-700">
                  I understand the risks involved and agree to the terms and conditions of this investment.
                </span>
              </label>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3">
              <button
                onClick={() => setStep(1)}
                className="flex-1 py-3 rounded-2xl font-semibold text-gray-700 bg-gray-100 hover:bg-gray-200 transition-colors"
              >
                Back
              </button>
              <button
                onClick={handleConfirm}
                disabled={!agreedToRisks}
                className={`flex-1 py-3 rounded-2xl font-semibold text-white transition-all duration-200 ${
                  agreedToRisks
                    ? `bg-gradient-to-r ${getRiskGradient(bot.riskLevel)} hover:shadow-lg transform hover:scale-105`
                    : 'bg-gray-300 cursor-not-allowed'
                }`}
              >
                <div className="flex items-center justify-center space-x-2">
                  <FontAwesomeIcon icon={ICON_NAMES.INVEST} />
                  <span>Confirm Investment</span>
                </div>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
