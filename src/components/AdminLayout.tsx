/**
 * ==============================================
 * ADMIN LAYOUT COMPONENT
 * ==============================================
 */

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';

interface AdminLayoutProps {
  children: React.ReactNode;
  currentPage: string;
  onNavigate: (page: string) => void;
  onLogout: () => void;
}

export function AdminLayout({ children, currentPage, onNavigate, onLogout }: AdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const navigation = [
    { name: 'Dashboard', href: 'dashboard', icon: ICON_NAMES.HOME },
    { name: 'Users', href: 'users', icon: ICON_NAMES.USER },
    { name: 'Trading Bots', href: 'bots', icon: ICON_NAMES.ROBOT },
    { name: 'Wallets', href: 'wallets', icon: ICON_NAMES.FOLDER },
    { name: 'Transactions', href: 'transactions', icon: ICON_NAMES.WALLET },
    { name: 'Support Chat', href: 'support', icon: ICON_NAMES.CHAT },
    { name: 'Analytics', href: 'analytics', icon: ICON_NAMES.CHART },
    { name: 'Audit Logs', href: 'logs', icon: ICON_NAMES.FILE },
    { name: 'Settings', href: 'settings', icon: ICON_NAMES.SETTINGS },
  ];

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        >
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75"></div>
        </div>
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-gray-800 transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="flex items-center justify-between h-16 px-4 bg-gray-900">
          <div className="flex items-center space-x-2">
            <FontAwesomeIcon icon={ICON_NAMES.SHIELD} className="h-8 w-8 text-blue-400" />
            <span className="text-xl font-bold text-white">Admin Portal</span>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden text-gray-400 hover:text-white"
          >
            <FontAwesomeIcon icon={ICON_NAMES.CLOSE} className="h-6 w-6" />
          </button>
        </div>

        {/* Navigation */}
        <nav className="mt-8 px-4">
          <div className="space-y-2">
            {navigation.map((item) => (
              <button
                key={item.name}
                onClick={() => {
                  onNavigate(item.href);
                  setSidebarOpen(false);
                }}
                className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
                  currentPage === item.href
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <FontAwesomeIcon icon={item.icon} className="mr-3 h-5 w-5" />
                {item.name}
              </button>
            ))}
          </div>
        </nav>

        {/* User info and logout */}
        <div className="absolute bottom-0 left-0 right-0 p-4 bg-gray-900">
          <div className="flex items-center space-x-3 mb-4">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 rounded-full bg-blue-600 flex items-center justify-center">
                <FontAwesomeIcon icon={ICON_NAMES.USER} className="h-5 w-5 text-white" />
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-white truncate">
                Admin User
              </p>
              <p className="text-xs text-gray-400 truncate">
                SUPER ADMIN
              </p>
            </div>
          </div>
          <button
            onClick={onLogout}
            className="w-full flex items-center px-4 py-2 text-sm font-medium text-gray-300 hover:bg-gray-700 hover:text-white rounded-lg transition-colors"
          >
            <FontAwesomeIcon icon={ICON_NAMES.LOGOUT} className="mr-3 h-5 w-5" />
            Sign Out
          </button>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-10 bg-gray-800 border-b border-gray-700">
          <div className="flex items-center justify-between h-16 px-4">
            <div className="flex items-center">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden text-gray-400 hover:text-white mr-4"
              >
                <FontAwesomeIcon icon={ICON_NAMES.MENU} className="h-6 w-6" />
              </button>
              <h1 className="text-xl font-semibold text-white capitalize">
                {currentPage === 'dashboard' ? 'Dashboard' : currentPage.replace('-', ' ')}
              </h1>
            </div>

            <div className="flex items-center space-x-4">
              {/* Notifications */}
              <button className="text-gray-400 hover:text-white relative">
                <FontAwesomeIcon icon={ICON_NAMES.NOTIFICATION} className="h-6 w-6" />
                <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"></span>
              </button>

              {/* User menu */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-300">Admin</span>
                <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                  <FontAwesomeIcon icon={ICON_NAMES.USER} className="h-4 w-4 text-white" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
