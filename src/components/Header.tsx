import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';

interface HeaderProps {
  user: any;
  onLogout: () => void;
}

export const Header: React.FC<HeaderProps> = ({ onLogout }) => {
  return (
    <header className="glass-effect border-b border-pink-100 px-4 md:px-6 py-4 animate-fade-in md:ml-80">
      <div className="flex items-center justify-between ml-16 md:ml-0">
        <div className="flex items-center space-x-3">
          <FontAwesomeIcon icon={ICON_NAMES.ROCKET} className="text-purple-400 text-2xl" />
          <span className="text-2xl font-black text-gray-900">MemeBot Pro</span>
        </div>

          {/* Logout - Desktop only */}
          <button
            onClick={onLogout}
            className="hidden md:flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-2xl transition-all-smooth transform hover:scale-105"
          >
            <FontAwesomeIcon icon={ICON_NAMES.LOGOUT} size="sm" />
            <span className="text-sm font-semibold">Logout</span>
          </button>
      </div>
    </header>
  );
};