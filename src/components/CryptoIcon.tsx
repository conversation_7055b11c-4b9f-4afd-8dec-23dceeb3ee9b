/**
 * ==============================================
 * CRYPTO ICON COMPONENT
 * ==============================================
 * 
 * Simple, clean crypto icons using local SVGs or styled text
 */

import React from 'react';

interface CryptoIconProps {
  symbol: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export const CryptoIcon: React.FC<CryptoIconProps> = ({ 
  symbol, 
  size = 'md', 
  className = '' 
}) => {
  const sizeClasses = {
    sm: 'w-6 h-6 text-xs',
    md: 'w-8 h-8 text-sm',
    lg: 'w-12 h-12 text-base',
    xl: 'w-16 h-16 text-lg'
  };

  const getIconColor = (symbol: string) => {
    const colors = {
      'BTC': 'bg-orange-500',
      'ETH': 'bg-blue-500',
      'USDT': 'bg-green-500',
      'USDC': 'bg-blue-600',
      'BNB': 'bg-yellow-500',
      'DOGE': 'bg-yellow-600',
      'SHIB': 'bg-orange-600',
      'PEPE': 'bg-green-600',
      'BONK': 'bg-orange-400',
      'WIF': 'bg-purple-500',
      'MEME': 'bg-pink-500',
      'FLOKI': 'bg-orange-500'
    };
    return colors[symbol as keyof typeof colors] || 'bg-gray-500';
  };

  const getIconPath = (symbol: string) => {
    const iconMap: { [key: string]: string } = {
      'SOL': '/crypto-icons/sol.svg',
      'BTC': '/crypto-icons/btc.svg',
      'ETH': '/crypto-icons/eth.svg',
      'USDT': '/crypto-icons/usdt.svg',
      'USDC': '/crypto-icons/usdc.svg',
    };
    return iconMap[symbol];
  };

  const iconPath = getIconPath(symbol);

  if (iconPath) {
    return (
      <div className={`${sizeClasses[size]} rounded-xl flex items-center justify-center ${className}`}>
        <img
          src={iconPath}
          alt={symbol}
          className="w-full h-full object-contain"
        />
      </div>
    );
  }

  // Fallback for symbols without icons
  return (
    <div className={`${sizeClasses[size]} ${getIconColor(symbol)} rounded-xl flex items-center justify-center ${className}`}>
      <div className="w-full h-full flex items-center justify-center font-bold text-white">
        {symbol.substring(0, 2)}
      </div>
    </div>
  );
};
