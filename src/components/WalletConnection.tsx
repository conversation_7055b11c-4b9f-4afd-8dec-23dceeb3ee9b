/**
 * ==============================================
 * WALLET CONNECTION COMPONENT
 * ==============================================
 * 
 * Custom wallet connection component that matches our design system.
 * Provides a sleek interface for connecting Solana wallets.
 */

import React, { useState } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';
import { colors, components, animations } from '../lib/designSystem';
import { cryptoIcons, walletIcons, assetUtils } from '../lib/cryptoAssets';

interface WalletConnectionProps {
  onConnect?: () => void;
  onDisconnect?: () => void;
  className?: string;
}

export const WalletConnection: React.FC<WalletConnectionProps> = ({
  onConnect,
  onDisconnect,
  className = '',
}) => {
  const { wallet, connect, disconnect, connecting, connected, publicKey } = useWallet();
  const [showWalletModal, setShowWalletModal] = useState(false);

  // Popular Solana wallets with their info
  const supportedWallets = [
    {
      name: 'Phantom',
      icon: walletIcons.phantom,
      description: 'Most popular Solana wallet',
      url: 'https://phantom.app/',
    },
    {
      name: 'Solflare',
      icon: walletIcons.solflare,
      description: 'Feature-rich Solana wallet',
      url: 'https://solflare.com/',
    },
    {
      name: 'Sollet',
      icon: walletIcons.sollet,
      description: 'Web-based Solana wallet',
      url: 'https://www.sollet.io/',
    },
  ];

  const handleConnect = async () => {
    try {
      await connect();
      setShowWalletModal(false);
      onConnect?.();
    } catch (error) {
      console.error('Failed to connect wallet:', error);
    }
  };

  const handleDisconnect = async () => {
    try {
      await disconnect();
      onDisconnect?.();
    } catch (error) {
      console.error('Failed to disconnect wallet:', error);
    }
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  // If wallet is connected, show wallet info
  if (connected && publicKey) {
    return (
      <div className={`${className}`}>
        <div className="glass-effect rounded-2xl p-4 border border-white/20">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                <FontAwesomeIcon icon={ICON_NAMES.WALLET} className="text-white" />
              </div>
              <div>
                <div className="text-sm font-semibold text-gray-900">
                  {wallet?.adapter.name || 'Connected'}
                </div>
                <div className="text-xs text-gray-600 font-mono">
                  {formatAddress(publicKey.toString())}
                </div>
              </div>
            </div>
            <button
              onClick={handleDisconnect}
              className="px-3 py-1.5 text-xs font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors"
            >
              Disconnect
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Connect Wallet Button */}
      <div className={`${className}`}>
        <button
          onClick={() => setShowWalletModal(true)}
          disabled={connecting}
          className={`
            w-full px-6 py-3 rounded-2xl font-semibold text-white
            bg-gradient-to-r from-purple-500 to-pink-500
            hover:from-purple-600 hover:to-pink-600
            disabled:opacity-50 disabled:cursor-not-allowed
            transform hover:scale-105 transition-all duration-200
            shadow-glow
            ${animations.transition.smooth}
          `}
        >
          <div className="flex items-center justify-center space-x-2">
            {connecting ? (
              <>
                <FontAwesomeIcon icon={ICON_NAMES.LOADING} className="animate-spin" />
                <span>Connecting...</span>
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={ICON_NAMES.WALLET} />
                <span>Connect Wallet</span>
              </>
            )}
          </div>
        </button>
      </div>

      {/* Wallet Selection Modal */}
      {showWalletModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
          <div className="bg-white rounded-3xl p-6 w-full max-w-md shadow-2xl animate-scale-in">
            {/* Modal Header */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900">Connect Wallet</h2>
              <button
                onClick={() => setShowWalletModal(false)}
                className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
              >
                <FontAwesomeIcon icon={ICON_NAMES.CLOSE} className="text-gray-500" />
              </button>
            </div>

            {/* Solana Network Info */}
            <div className="mb-6 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl border border-purple-100">
              <div className="flex items-center space-x-3">
                <div 
                  className="w-8 h-8 flex items-center justify-center"
                  dangerouslySetInnerHTML={{ __html: cryptoIcons.solana }}
                />
                <div>
                  <div className="text-sm font-semibold text-gray-900">Solana Network</div>
                  <div className="text-xs text-gray-600">Fast, secure, and low-cost transactions</div>
                </div>
              </div>
            </div>

            {/* Wallet Options */}
            <div className="space-y-3 mb-6">
              {supportedWallets.map((walletInfo) => (
                <button
                  key={walletInfo.name}
                  onClick={handleConnect}
                  className="w-full p-4 rounded-2xl border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all duration-200 text-left group"
                >
                  <div className="flex items-center space-x-4">
                    <div 
                      className="w-12 h-12 flex items-center justify-center rounded-xl bg-gray-50 group-hover:bg-white transition-colors"
                      dangerouslySetInnerHTML={{ __html: walletInfo.icon }}
                    />
                    <div className="flex-1">
                      <div className="text-sm font-semibold text-gray-900">{walletInfo.name}</div>
                      <div className="text-xs text-gray-600">{walletInfo.description}</div>
                    </div>
                    <FontAwesomeIcon 
                      icon={ICON_NAMES.FORWARD} 
                      className="text-gray-400 group-hover:text-purple-500 transition-colors" 
                    />
                  </div>
                </button>
              ))}
            </div>

            {/* Help Text */}
            <div className="text-center">
              <p className="text-xs text-gray-500 mb-2">
                Don't have a Solana wallet?
              </p>
              <a
                href="https://phantom.app/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-xs text-purple-600 hover:text-purple-700 font-medium"
              >
                Download Phantom Wallet
                <FontAwesomeIcon icon={ICON_NAMES.EXTERNAL_LINK} className="ml-1" />
              </a>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
