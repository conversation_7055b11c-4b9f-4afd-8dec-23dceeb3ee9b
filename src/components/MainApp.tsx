/**
 * ==============================================
 * MAIN APP COMPONENT
 * ==============================================
 * 
 * Complete memecoin trading bot platform with all pages and functionality.
 * Includes navigation, state management, and modal handling.
 */

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';
import { colors, components, animations } from '../lib/designSystem';
import { formatCurrency } from '../lib/utils';

// Import all pages and components
import { BotMarketplace } from '../pages/BotMarketplace';
import { EnhancedPortfolio } from '../pages/EnhancedPortfolio';
import { DepositWithdraw } from '../pages/DepositWithdraw';
import { TransactionHistory } from '../pages/TransactionHistory';
import { UserSettings } from '../pages/UserSettings';
import { InvestmentModal } from './InvestmentModal';

type Page = 'dashboard' | 'marketplace' | 'portfolio' | 'deposit-withdraw' | 'transactions' | 'settings';

interface UserBalances extends Record<string, number> {
  USDT: number;
  USDC: number;
  BTC: number;
  ETH: number;
  DOGE: number;
  SHIB: number;
  PEPE: number;
}

export const MainApp: React.FC = () => {
  const [currentPage, setCurrentPage] = useState<Page>('dashboard');
  const [investmentModal, setInvestmentModal] = useState<{
    isOpen: boolean;
    botId: string | null;
  }>({ isOpen: false, botId: null });

  // Mock user balances - in real app this would come from API
  const [userBalances, setUserBalances] = useState<UserBalances>({
    USDT: 5420.50,
    USDC: 2150.00,
    BTC: 0.15,
    ETH: 1.25,
    DOGE: 15000,
    SHIB: 50000000,
    PEPE: 1000000,
  });

  // Mock bot data for investment modal
  const getBotData = (botId: string) => {
    const bots = {
      '1': {
        id: '1',
        name: 'Conservative Growth',
        riskLevel: 'low' as const,
        minInvestment: 50,
        maxInvestment: 10000,
        expectedReturn: 15,
        winRate: 78,
        description: 'Steady, low-risk growth with focus on established meme coins',
      },
      '2': {
        id: '2',
        name: 'Momentum Hunter',
        riskLevel: 'medium' as const,
        minInvestment: 100,
        maxInvestment: 25000,
        expectedReturn: 35,
        winRate: 65,
        description: 'Captures trending meme coins with moderate risk approach',
      },
      '3': {
        id: '3',
        name: 'Alpha Seeker',
        riskLevel: 'high' as const,
        minInvestment: 200,
        maxInvestment: 50000,
        expectedReturn: 75,
        winRate: 52,
        description: 'High-risk, high-reward strategy for maximum gains',
      },
    };
    return bots[botId as keyof typeof bots] || null;
  };

  const handleInvestClick = (botId: string) => {
    setInvestmentModal({ isOpen: true, botId });
  };

  const handleInvestConfirm = (amount: number, autoReinvest: boolean) => {
    // In real app, this would make API call to invest
    console.log('Investment confirmed:', { 
      botId: investmentModal.botId, 
      amount, 
      autoReinvest 
    });
    
    // Update user balance (subtract USDT for simplicity)
    setUserBalances(prev => ({
      ...prev,
      USDT: prev.USDT - amount,
    }));
    
    setInvestmentModal({ isOpen: false, botId: null });
  };

  const handleDeposit = (crypto: string, amount: number) => {
    // In real app, this would handle deposit logic
    console.log('Deposit:', { crypto, amount });
    setUserBalances(prev => ({
      ...prev,
      [crypto]: (prev[crypto as keyof UserBalances] || 0) + amount,
    }));
  };

  const handleWithdraw = (crypto: string, amount: number, address: string) => {
    // In real app, this would handle withdrawal logic
    console.log('Withdraw:', { crypto, amount, address });
    setUserBalances(prev => ({
      ...prev,
      [crypto]: Math.max(0, (prev[crypto as keyof UserBalances] || 0) - amount),
    }));
  };

  const totalBalance = Object.entries(userBalances).reduce((total, [crypto, amount]) => {
    // Mock prices for calculation
    const prices: Record<string, number> = {
      USDT: 1,
      USDC: 1,
      BTC: 65000,
      ETH: 3000,
      DOGE: 0.08,
      SHIB: 0.000008,
      PEPE: 0.000001,
    };
    return total + (amount * (prices[crypto] || 0));
  }, 0);

  const navigationItems = [
    { id: 'dashboard', label: 'Dashboard', icon: ICON_NAMES.HOME },
    { id: 'marketplace', label: 'Marketplace', icon: ICON_NAMES.ROBOT },
    { id: 'portfolio', label: 'Portfolio', icon: ICON_NAMES.WALLET },
    { id: 'deposit-withdraw', label: 'Funds', icon: ICON_NAMES.EXCHANGE },
    { id: 'transactions', label: 'History', icon: ICON_NAMES.CLOCK },
    { id: 'settings', label: 'Settings', icon: ICON_NAMES.SETTINGS },
  ];

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return (
          <div className="p-4 md:p-6 pb-32 md:pb-6">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-black text-gray-900 mb-2">Welcome to Morewise</h1>
              <p className="text-gray-600">Your memecoin trading bot platform</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 text-center">
                <FontAwesomeIcon icon={ICON_NAMES.ROBOT} className="text-4xl text-purple-500 mb-4" />
                <h3 className="text-lg font-bold text-gray-900 mb-2">Trading Bots</h3>
                <p className="text-gray-600 mb-4">Automated trading strategies</p>
                <button
                  onClick={() => setCurrentPage('marketplace')}
                  className="px-4 py-2 bg-purple-500 text-white rounded-xl hover:bg-purple-600 transition-colors"
                >
                  Explore Bots
                </button>
              </div>
              <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 text-center">
                <FontAwesomeIcon icon={ICON_NAMES.WALLET} className="text-4xl text-blue-500 mb-4" />
                <h3 className="text-lg font-bold text-gray-900 mb-2">Portfolio</h3>
                <p className="text-gray-600 mb-4">Track your investments</p>
                <button
                  onClick={() => setCurrentPage('portfolio')}
                  className="px-4 py-2 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors"
                >
                  View Portfolio
                </button>
              </div>
              <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 text-center">
                <FontAwesomeIcon icon={ICON_NAMES.EXCHANGE} className="text-4xl text-green-500 mb-4" />
                <h3 className="text-lg font-bold text-gray-900 mb-2">Funds</h3>
                <p className="text-gray-600 mb-4">Deposit and withdraw</p>
                <button
                  onClick={() => setCurrentPage('deposit-withdraw')}
                  className="px-4 py-2 bg-green-500 text-white rounded-xl hover:bg-green-600 transition-colors"
                >
                  Manage Funds
                </button>
              </div>
            </div>
          </div>
        );
      case 'marketplace':
        return <BotMarketplace onInvestClick={handleInvestClick} />;
      case 'portfolio':
        return (
          <EnhancedPortfolio
            onDepositClick={() => setCurrentPage('deposit-withdraw')}
            onWithdrawClick={() => setCurrentPage('deposit-withdraw')}
            onBotClick={(botId) => console.log('Bot clicked:', botId)}
          />
        );
      case 'deposit-withdraw':
        return (
          <DepositWithdraw
            userBalances={userBalances}
            onDeposit={handleDeposit}
            onWithdraw={handleWithdraw}
          />
        );
      case 'transactions':
        return <TransactionHistory />;
      case 'settings':
        return <UserSettings />;
      default:
        return (
          <div className="p-4 md:p-6 pb-32 md:pb-6">
            <div className="text-center">
              <h1 className="text-3xl font-black text-gray-900 mb-2">Welcome to Morewise</h1>
              <p className="text-gray-600">Your memecoin trading bot platform</p>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-lg border-b border-white/20 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                <FontAwesomeIcon icon={ICON_NAMES.ROBOT} className="text-white text-lg" />
              </div>
              <div>
                <h1 className="text-xl font-black text-gray-900">Morewise</h1>
                <p className="text-xs text-gray-600">Memecoin Trading Bots</p>
              </div>
            </div>

            {/* Balance Display */}
            <div className="hidden md:flex items-center space-x-4">
              <div className="text-right">
                <div className="text-sm text-gray-600">Total Balance</div>
                <div className="text-lg font-bold text-gray-900">
                  {formatCurrency(totalBalance)}
                </div>
              </div>
              <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
                <FontAwesomeIcon icon={ICON_NAMES.USER} className="text-white" />
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto md:ml-64">
        {renderCurrentPage()}
      </main>

      {/* Bottom Navigation */}
      <nav className="fixed bottom-0 left-0 right-0 bg-white/90 backdrop-blur-lg border-t border-white/20 md:hidden z-50">
        <div className="grid grid-cols-6 gap-1 p-2">
          {navigationItems.map((item) => (
            <button
              key={item.id}
              onClick={() => setCurrentPage(item.id as Page)}
              className={`flex flex-col items-center justify-center py-2 px-1 rounded-xl transition-all ${
                currentPage === item.id
                  ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <FontAwesomeIcon icon={item.icon} className="text-lg mb-1" />
              <span className="text-xs font-medium">{item.label}</span>
            </button>
          ))}
        </div>
      </nav>

      {/* Desktop Sidebar Navigation */}
      <nav className="hidden md:block fixed left-0 top-16 bottom-0 w-64 bg-white/80 backdrop-blur-lg border-r border-white/20 z-40">
        <div className="p-6 space-y-2">
          {navigationItems.map((item) => (
            <button
              key={item.id}
              onClick={() => setCurrentPage(item.id as Page)}
              className={`w-full flex items-center space-x-3 py-3 px-4 rounded-xl transition-all ${
                currentPage === item.id
                  ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <FontAwesomeIcon icon={item.icon} className="text-lg" />
              <span className="font-medium">{item.label}</span>
            </button>
          ))}
        </div>

        {/* Balance Card in Sidebar */}
        <div className="mx-6 mt-8 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl border border-purple-100">
          <div className="text-center">
            <div className="text-sm text-gray-600 mb-1">Total Portfolio</div>
            <div className="text-2xl font-bold text-gray-900 mb-2">
              {formatCurrency(totalBalance)}
            </div>
            <div className="grid grid-cols-2 gap-2">
              <button 
                onClick={() => setCurrentPage('deposit-withdraw')}
                className="py-2 px-3 bg-green-500 text-white rounded-lg text-xs font-medium hover:bg-green-600 transition-colors"
              >
                Deposit
              </button>
              <button 
                onClick={() => setCurrentPage('deposit-withdraw')}
                className="py-2 px-3 bg-red-500 text-white rounded-lg text-xs font-medium hover:bg-red-600 transition-colors"
              >
                Withdraw
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Investment Modal */}
      <InvestmentModal
        isOpen={investmentModal.isOpen}
        onClose={() => setInvestmentModal({ isOpen: false, botId: null })}
        onConfirm={handleInvestConfirm}
        bot={investmentModal.botId ? getBotData(investmentModal.botId) : null}
        userBalance={userBalances.USDT} // Using USDT as primary investment currency
      />

      {/* Desktop Content Offset - handled by CSS classes */}
    </div>
  );
};
