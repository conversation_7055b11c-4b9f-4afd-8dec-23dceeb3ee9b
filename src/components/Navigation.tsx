import React, { useState, useRef, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';

interface NavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  onLogout: () => void;
  isAdmin?: boolean;
}

export const Navigation: React.FC<NavigationProps> = ({
  activeTab,
  onTabChange,
  onLogout,
  isAdmin = false
}) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const sidebarRef = useRef<HTMLDivElement>(null);

  // All navigation items for sidebar
  const allNavItems = [
    { id: 'dashboard', icon: ICON_NAMES.HOME, label: 'Dashboard' },
    { id: 'bots', icon: ICON_NAMES.ROBOT, label: 'Trading Bots' },
    { id: 'portfolio', icon: ICON_NAMES.CHART, label: 'Portfolio' },
    { id: 'wallet', icon: ICON_NAMES.WALLET, label: 'Wallet' },
    { id: 'settings', icon: ICON_NAMES.SETTINGS, label: 'Settings' },
  ];

  // Mobile bottom navigation items (only 4 main items)
  const mobileNavItems = [
    { id: 'dashboard', icon: ICON_NAMES.HOME, label: 'Home' },
    { id: 'bots', icon: ICON_NAMES.ROBOT, label: 'Bots' },
    { id: 'portfolio', icon: ICON_NAMES.CHART, label: 'Portfolio' },
    { id: 'settings', icon: ICON_NAMES.SETTINGS, label: 'Settings' },
  ];

  const handleTabChange = (tab: string) => {
    onTabChange(tab);
    setIsSidebarOpen(false); // Close sidebar when tab is selected
  };

  const handleLogout = () => {
    onLogout();
    setIsSidebarOpen(false);
  };

  // Close sidebar when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (sidebarRef.current && !sidebarRef.current.contains(event.target as Node)) {
        setIsSidebarOpen(false);
      }
    };

    if (isSidebarOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isSidebarOpen]);

  return (
    <>
      {/* Mobile Menu Button - Bigger */}
      <button
        onClick={() => setIsSidebarOpen(!isSidebarOpen)}
        className="md:hidden fixed top-4 left-4 z-50 p-4 bg-gradient-to-r from-pink-500 to-pink-600 text-white rounded-xl shadow-lg"
      >
        <FontAwesomeIcon icon={isSidebarOpen ? ICON_NAMES.CLOSE : ICON_NAMES.MENU} className="text-xl" />
      </button>

      {/* Mobile Bottom Navigation - Always visible, no fade animation */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 glass-effect border-t border-pink-100 z-40">
        <div className="flex justify-around py-3">
          {mobileNavItems.map((item) => {
            const isActive = activeTab === item.id;
            return (
              <button
                key={item.id}
                onClick={() => handleTabChange(item.id)}
                className={`flex flex-col items-center py-2 px-4 rounded-2xl transition-all duration-300 ease-in-out transform ${
                  isActive
                    ? 'text-white bg-gradient-to-r from-pink-500 to-pink-600 shadow-glow scale-110'
                    : 'text-gray-600 hover:text-pink-600 hover:bg-pink-50 hover:scale-105'
                }`}
              >
                <FontAwesomeIcon
                  icon={item.icon}
                  className={`text-lg transition-all duration-300 ${isActive ? 'animate-bounce' : ''}`}
                />
                <span className="text-xs mt-1 font-medium">{item.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Mobile Sidebar Overlay */}
      {isSidebarOpen && (
        <div className="md:hidden fixed inset-0 bg-black bg-opacity-50 z-40" onClick={() => setIsSidebarOpen(false)} />
      )}

      {/* Mobile/Desktop Sidebar Navigation */}
      <div
        ref={sidebarRef}
        className={`fixed left-0 top-0 h-full w-80 glass-effect border-r border-pink-100 flex-col z-50 transition-transform duration-300 ${
          isSidebarOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'
        } ${isSidebarOpen ? 'flex' : 'hidden md:flex'}`}
      >
        <div className="p-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <FontAwesomeIcon icon={ICON_NAMES.ROCKET} className="text-purple-400 text-2xl" />
              <span className="text-xl font-black text-gray-900">MemeBot Pro</span>
            </div>
            <button
              onClick={() => setIsSidebarOpen(false)}
              className="md:hidden p-2 text-gray-500 hover:text-gray-700 rounded-lg"
            >
              <FontAwesomeIcon icon={ICON_NAMES.CLOSE} />
            </button>
          </div>
        </div>

        <nav className="flex-1 px-6">
          {allNavItems.map((item) => {
            const isActive = activeTab === item.id;
            return (
              <button
                key={item.id}
                onClick={() => handleTabChange(item.id)}
                className={`w-full flex items-center space-x-4 py-4 px-6 rounded-2xl mb-3 transition-all duration-200 ease-in-out hover:scale-[1.02] ${
                  isActive
                    ? 'bg-gradient-to-r from-pink-500 to-pink-600 text-white shadow-glow'
                    : 'text-gray-700 hover:bg-pink-50 hover:text-pink-600'
                }`}
              >
                <FontAwesomeIcon icon={item.icon} size="lg" />
                <span className="font-semibold text-lg">{item.label}</span>
              </button>
            );
          })}
        </nav>

        <div className="p-6 border-t border-pink-100">
          <button
            onClick={handleLogout}
            className="w-full flex items-center space-x-4 py-4 px-6 rounded-2xl text-gray-700 hover:bg-red-50 hover:text-red-600 transition-all-smooth transform hover:scale-105"
          >
            <FontAwesomeIcon icon={ICON_NAMES.LOGOUT} size="lg" />
            <span className="font-semibold text-lg">Logout</span>
          </button>
        </div>
      </div>
    </>
  );
};