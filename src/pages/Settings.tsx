// ==============================================
// SETTINGS PAGE
// ==============================================
// User profile, security, and app preferences
// Modify settings sections based on your requirements

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';
import { User } from '../types';

interface SettingsProps {
  user: User;
  onUserUpdate: (user: User) => void;
}

export const Settings: React.FC<SettingsProps> = ({ user, onUserUpdate }) => {
  const [activeSection, setActiveSection] = useState<string | null>(null);
  const [editingProfile, setEditingProfile] = useState(false);
  const [profileData, setProfileData] = useState(user);

  const handleProfileUpdate = () => {
    onUserUpdate(profileData);
    setEditingProfile(false);
  };

  const settingSections = [
    {
      id: 'profile',
      title: 'Profile & Personal Info',
      icon: ICON_NAMES.USER,
      description: 'Update your personal information and contact details'
    },
    {
      id: 'security',
      title: 'Security & Privacy',
      icon: ICON_NAMES.SHIELD,
      description: 'Manage your security settings and privacy preferences'
    },
    {
      id: 'notifications',
      title: 'Notifications',
      icon: ICON_NAMES.NOTIFICATION,
      description: 'Control how and when you receive notifications'
    },
    {
      id: 'devices',
      title: 'Connected Devices',
      icon: ICON_NAMES.PHONE,
      description: 'Manage devices with access to your account'
    },
    {
      id: 'cards',
      title: 'Wallet Settings',
      icon: ICON_NAMES.WALLET,
      description: 'Manage your wallet and payment preferences'
    },
    {
      id: 'statements',
      title: 'Trading History',
      icon: ICON_NAMES.CHART,
      description: 'Access trading history and manage document preferences'
    },
    {
      id: 'help',
      title: 'Help & Support',
      icon: ICON_NAMES.INFO,
      description: 'Get help and contact customer support'
    }
  ];

  const renderProfileSection = () => (
    <div className="bg-white rounded-xl border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">Profile Information</h2>
        <button
          onClick={() => setEditingProfile(!editingProfile)}
          className="flex items-center space-x-2 text-purple-600 hover:text-purple-700"
        >
          <FontAwesomeIcon icon={ICON_NAMES.EDIT} />
          <span className="text-sm font-medium">
            {editingProfile ? 'Cancel' : 'Edit'}
          </span>
        </button>
      </div>

      {editingProfile ? (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
            <input
              type="text"
              value={profileData.name || ''}
              onChange={(e) => setProfileData({...profileData, name: e.target.value})}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
            <input
              type="email"
              value={profileData.email || ''}
              onChange={(e) => setProfileData({...profileData, email: e.target.value})}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Username</label>
            <input
              type="text"
              value={profileData.username}
              onChange={(e) => setProfileData({...profileData, username: e.target.value})}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
          
          <div className="flex space-x-3 pt-4">
            <button
              onClick={() => setEditingProfile(false)}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleProfileUpdate}
              className="flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700"
            >
              Save Changes
            </button>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg">
            <FontAwesomeIcon icon={ICON_NAMES.EMAIL} className="text-gray-400" />
            <div>
              <div className="text-sm text-gray-600">Email</div>
              <div className="font-medium text-gray-900">{user.email || 'Not provided'}</div>
            </div>
          </div>

          <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg">
            <FontAwesomeIcon icon={ICON_NAMES.PHONE} className="text-gray-400" />
            <div>
              <div className="text-sm text-gray-600">Username</div>
              <div className="font-medium text-gray-900">{user.username}</div>
            </div>
          </div>

          <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg">
            <FontAwesomeIcon icon={ICON_NAMES.WALLET} className="text-gray-400" />
            <div>
              <div className="text-sm text-gray-600">Wallet Address</div>
              <div className="font-medium text-gray-900">
                {user.wallet_address ? `${user.wallet_address.slice(0, 6)}...${user.wallet_address.slice(-4)}` : 'Not connected'}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const renderSecuritySection = () => (
    <div className="bg-white rounded-xl border border-gray-200 p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-6">Security Settings</h2>
      
      <div className="space-y-4">
        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-3">
            <FontAwesomeIcon icon={ICON_NAMES.LOCK} className="text-gray-400" />
            <div>
              <div className="font-medium text-gray-900">Change Password</div>
              <div className="text-sm text-gray-600">Last changed 3 months ago</div>
            </div>
          </div>
          <FontAwesomeIcon icon={ICON_NAMES.ARROW_RIGHT} className="text-gray-400" />
        </div>

        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-3">
            <FontAwesomeIcon icon={ICON_NAMES.SHIELD} className="text-gray-400" />
            <div>
              <div className="font-medium text-gray-900">Wallet Security</div>
              <div className="text-sm text-gray-600">Enhanced wallet protection</div>
            </div>
          </div>
          <div className="flex items-center">
            <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-purple-600 transition-colors">
              <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" />
            </button>
          </div>
        </div>

        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-3">
            <FontAwesomeIcon icon={ICON_NAMES.PHONE} className="text-gray-400" />
            <div>
              <div className="font-medium text-gray-900">Two-Factor Authentication</div>
              <div className="text-sm text-gray-600">Extra security for your account</div>
            </div>
          </div>
          <div className="flex items-center">
            <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-300 transition-colors">
              <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="p-4 pb-24 md:pb-4 space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-700 rounded-full flex items-center justify-center">
          <span className="text-white text-xl font-bold">
            {(user.name || user.username).split(' ').map(n => n[0]).join('').toUpperCase()}
          </span>
        </div>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{user.name || user.username}</h1>
          <p className="text-gray-600">Crypto trader since 2024</p>
        </div>
      </div>

      {/* Active Section Content */}
      {activeSection === 'profile' && renderProfileSection()}
      {activeSection === 'security' && renderSecuritySection()}
      
      {/* Settings Menu */}
      {!activeSection && (
        <div className="space-y-3">
          {settingSections.map((section) => {
            return (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className="w-full bg-white rounded-xl border border-gray-200 p-4 hover:shadow-md transition-shadow text-left"
              >
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                    <FontAwesomeIcon icon={section.icon} className="text-purple-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900">{section.title}</h3>
                    <p className="text-sm text-gray-600">{section.description}</p>
                  </div>
                  <FontAwesomeIcon icon={ICON_NAMES.ARROW_RIGHT} className="text-gray-400" />
                </div>
              </button>
            );
          })}
        </div>
      )}

      {/* Back Button for Active Sections */}
      {activeSection && (
        <button
          onClick={() => setActiveSection(null)}
          className="fixed bottom-20 md:bottom-4 right-4 bg-purple-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-purple-700 shadow-lg"
        >
          Back to Settings
        </button>
      )}

      {/* App Info */}
      <div className="bg-gray-50 rounded-xl p-4 text-center">
        <div className="text-sm text-gray-600 mb-2">MemeBot Pro</div>
        <div className="text-xs text-gray-500">Version 1.0.0 • Build 2024.1</div>
        <div className="text-xs text-gray-500 mt-2">
          © 2024 MemeBot Pro. All rights reserved.
        </div>
        <div className="text-xs text-gray-500">
          Automated crypto trading platform. Trade responsibly.
        </div>
      </div>
    </div>
  );
};