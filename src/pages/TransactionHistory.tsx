/**
 * ==============================================
 * TRANSACTION HISTORY PAGE
 * ==============================================
 * 
 * Complete transaction history with filtering, search, and export functionality.
 * Shows deposits, withdrawals, bot investments, and trading activity.
 */

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';
import { colors, components } from '../lib/designSystem';
import { cryptoIcons, assetUtils } from '../lib/cryptoAssets';
import { formatCurrency, formatPercentage } from '../lib/utils';

interface Transaction {
  id: string;
  type: 'deposit' | 'withdraw' | 'bot_investment' | 'bot_profit' | 'bot_loss';
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  timestamp: string;
  description: string;
  txHash?: string;
  botName?: string;
  fee?: number;
}

export const TransactionHistory: React.FC = () => {
  const [activeFilter, setActiveFilter] = useState<'all' | 'deposits' | 'withdrawals' | 'bots'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d');

  // Mock transaction data
  const transactions: Transaction[] = [
    {
      id: '1',
      type: 'deposit',
      amount: 5000,
      currency: 'USDT',
      status: 'completed',
      timestamp: '2024-03-15T10:30:00Z',
      description: 'USDT Deposit via TRC20',
      txHash: '0x1234...5678',
      fee: 1,
    },
    {
      id: '2',
      type: 'bot_investment',
      amount: 2000,
      currency: 'USDT',
      status: 'completed',
      timestamp: '2024-03-15T11:15:00Z',
      description: 'Investment in Conservative Growth Bot',
      botName: 'Conservative Growth',
    },
    {
      id: '3',
      type: 'bot_profit',
      amount: 156.80,
      currency: 'USDT',
      status: 'completed',
      timestamp: '2024-03-16T09:45:00Z',
      description: 'Daily profit from Momentum Hunter Bot',
      botName: 'Momentum Hunter',
    },
    {
      id: '4',
      type: 'withdraw',
      amount: 1000,
      currency: 'USDT',
      status: 'pending',
      timestamp: '2024-03-16T14:20:00Z',
      description: 'USDT Withdrawal to external wallet',
      txHash: '0xabcd...efgh',
      fee: 5,
    },
    {
      id: '5',
      type: 'bot_investment',
      amount: 3000,
      currency: 'USDT',
      status: 'completed',
      timestamp: '2024-03-14T16:30:00Z',
      description: 'Investment in Alpha Seeker Bot',
      botName: 'Alpha Seeker',
    },
    {
      id: '6',
      type: 'deposit',
      amount: 0.05,
      currency: 'BTC',
      status: 'completed',
      timestamp: '2024-03-13T08:15:00Z',
      description: 'Bitcoin Deposit',
      txHash: '0x9876...5432',
      fee: 0.0001,
    },
  ];

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'deposit': return ICON_NAMES.DEPOSIT;
      case 'withdraw': return ICON_NAMES.WITHDRAW;
      case 'bot_investment': return ICON_NAMES.ROBOT;
      case 'bot_profit': return ICON_NAMES.TRENDING_UP;
      case 'bot_loss': return ICON_NAMES.TRENDING_DOWN;
      default: return ICON_NAMES.EXCHANGE;
    }
  };

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'deposit': return 'text-green-600 bg-green-100';
      case 'withdraw': return 'text-red-600 bg-red-100';
      case 'bot_investment': return 'text-purple-600 bg-purple-100';
      case 'bot_profit': return 'text-green-600 bg-green-100';
      case 'bot_loss': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'failed': return 'text-red-600 bg-red-100';
      case 'cancelled': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const filteredTransactions = transactions.filter(tx => {
    // Filter by type
    if (activeFilter === 'deposits' && tx.type !== 'deposit') return false;
    if (activeFilter === 'withdrawals' && tx.type !== 'withdraw') return false;
    if (activeFilter === 'bots' && !tx.type.includes('bot')) return false;

    // Filter by search term
    if (searchTerm && !tx.description.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }

    // Filter by date range
    const txDate = new Date(tx.timestamp);
    const now = new Date();
    const daysDiff = Math.floor((now.getTime() - txDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (dateRange === '7d' && daysDiff > 7) return false;
    if (dateRange === '30d' && daysDiff > 30) return false;
    if (dateRange === '90d' && daysDiff > 90) return false;

    return true;
  });

  const totalDeposits = transactions
    .filter(tx => tx.type === 'deposit' && tx.status === 'completed')
    .reduce((sum, tx) => sum + tx.amount, 0);

  const totalWithdrawals = transactions
    .filter(tx => tx.type === 'withdraw' && tx.status === 'completed')
    .reduce((sum, tx) => sum + tx.amount, 0);

  const totalBotProfits = transactions
    .filter(tx => tx.type === 'bot_profit')
    .reduce((sum, tx) => sum + tx.amount, 0);

  return (
    <div className="p-4 md:p-6 pb-32 md:pb-6 space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-2xl md:text-3xl font-black text-gray-900 mb-2">Transaction History</h1>
        <p className="text-gray-600">Track all your deposits, withdrawals, and bot activities</p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-100">
          <div className="flex items-center justify-between mb-2">
            <span className="text-green-700 font-medium">Total Deposits</span>
            <FontAwesomeIcon icon={ICON_NAMES.DEPOSIT} className="text-green-600" />
          </div>
          <div className="text-2xl font-bold text-green-800">
            {formatCurrency(totalDeposits)}
          </div>
        </div>

        <div className="bg-gradient-to-r from-red-50 to-pink-50 rounded-2xl p-6 border border-red-100">
          <div className="flex items-center justify-between mb-2">
            <span className="text-red-700 font-medium">Total Withdrawals</span>
            <FontAwesomeIcon icon={ICON_NAMES.WITHDRAW} className="text-red-600" />
          </div>
          <div className="text-2xl font-bold text-red-800">
            {formatCurrency(totalWithdrawals)}
          </div>
        </div>

        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-6 border border-purple-100">
          <div className="flex items-center justify-between mb-2">
            <span className="text-purple-700 font-medium">Bot Profits</span>
            <FontAwesomeIcon icon={ICON_NAMES.ROBOT} className="text-purple-600" />
          </div>
          <div className="text-2xl font-bold text-purple-800">
            +{formatCurrency(totalBotProfits)}
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          {/* Filter Tabs */}
          <div className="flex space-x-2">
            {[
              { key: 'all', label: 'All' },
              { key: 'deposits', label: 'Deposits' },
              { key: 'withdrawals', label: 'Withdrawals' },
              { key: 'bots', label: 'Bot Activity' },
            ].map((filter) => (
              <button
                key={filter.key}
                onClick={() => setActiveFilter(filter.key as any)}
                className={`px-4 py-2 rounded-xl font-medium transition-all ${
                  activeFilter === filter.key
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {filter.label}
              </button>
            ))}
          </div>

          {/* Search and Date Range */}
          <div className="flex space-x-3">
            <div className="relative">
              <FontAwesomeIcon 
                icon={ICON_NAMES.SEARCH} 
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" 
              />
              <input
                type="text"
                placeholder="Search transactions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>

            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value as any)}
              className="px-4 py-2 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="all">All time</option>
            </select>
          </div>
        </div>
      </div>

      {/* Transaction List */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-bold text-gray-900">
              Recent Transactions ({filteredTransactions.length})
            </h3>
            <button className="flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-600 rounded-xl hover:bg-gray-200 transition-colors">
              <FontAwesomeIcon icon={ICON_NAMES.DOWNLOAD} />
              <span>Export</span>
            </button>
          </div>
        </div>

        <div className="divide-y divide-gray-100">
          {filteredTransactions.map((transaction) => (
            <div key={transaction.id} className="p-6 hover:bg-gray-50 transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${getTransactionColor(transaction.type)}`}>
                    <FontAwesomeIcon icon={getTransactionIcon(transaction.type)} className="text-lg" />
                  </div>
                  
                  <div>
                    <div className="font-semibold text-gray-900">{transaction.description}</div>
                    <div className="text-sm text-gray-600">{formatDate(transaction.timestamp)}</div>
                    {transaction.txHash && (
                      <div className="text-xs text-blue-600 font-mono">
                        {transaction.txHash}
                      </div>
                    )}
                  </div>
                </div>

                <div className="text-right">
                  <div className={`text-lg font-bold ${
                    transaction.type === 'deposit' || transaction.type === 'bot_profit' 
                      ? 'text-green-600' 
                      : 'text-gray-900'
                  }`}>
                    {(transaction.type === 'deposit' || transaction.type === 'bot_profit') ? '+' : '-'}
                    {formatCurrency(transaction.amount)} {transaction.currency}
                  </div>
                  
                  <div className="flex items-center justify-end space-x-2 mt-1">
                    <span className={`px-2 py-1 rounded-lg text-xs font-medium ${getStatusColor(transaction.status)}`}>
                      {transaction.status.toUpperCase()}
                    </span>
                    {transaction.fee && (
                      <span className="text-xs text-gray-500">
                        Fee: {transaction.fee} {transaction.currency}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredTransactions.length === 0 && (
          <div className="p-12 text-center">
            <FontAwesomeIcon icon={ICON_NAMES.SEARCH} className="text-4xl text-gray-300 mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No transactions found</h3>
            <p className="text-gray-600">Try adjusting your filters or search terms</p>
          </div>
        )}
      </div>
    </div>
  );
};
