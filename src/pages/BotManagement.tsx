/**
 * ==============================================
 * BOT MANAGEMENT AND TRADING CONTROL PAGE
 * ==============================================
 * 
 * Real-time bot monitoring, trading controls, performance tracking,
 * and bot management interface.
 */

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';
import { colors, components, animations } from '../lib/designSystem';
import { formatCurrency, formatPercentage } from '../lib/utils';

interface TradingBot {
  id: string;
  name: string;
  status: 'active' | 'paused' | 'stopped' | 'error';
  strategy: string;
  riskLevel: 'low' | 'medium' | 'high';
  investedAmount: number;
  currentValue: number;
  totalReturn: number;
  returnPercent: number;
  dailyReturn: number;
  tradesCount: number;
  winRate: number;
  lastTrade: string;
  createdAt: string;
  tradingPairs: string[];
  config: {
    stopLoss: number;
    takeProfit: number;
    maxDrawdown: number;
    autoReinvest: boolean;
  };
  performance: {
    totalTrades: number;
    winningTrades: number;
    losingTrades: number;
    avgProfit: number;
    avgLoss: number;
    maxDrawdown: number;
    sharpeRatio: number;
  };
}

interface BotManagementProps {
  onSetupBot?: () => void;
  onEditBot?: (bot: TradingBot) => void;
  onBackToMarketplace?: () => void;
}

export const BotManagement: React.FC<BotManagementProps> = ({
  onSetupBot,
  onEditBot,
  onBackToMarketplace,
}) => {
  const [bots, setBots] = useState<TradingBot[]>([
    {
      id: 'bot-1',
      name: 'DCA Master Pro',
      status: 'active',
      strategy: 'Dollar Cost Averaging',
      riskLevel: 'medium',
      investedAmount: 1000,
      currentValue: 1150,
      totalReturn: 150,
      returnPercent: 15,
      dailyReturn: 2.5,
      tradesCount: 24,
      winRate: 75,
      lastTrade: '2 minutes ago',
      createdAt: '2024-01-15',
      tradingPairs: ['BTC/USDT', 'ETH/USDT'],
      config: {
        stopLoss: 5,
        takeProfit: 15,
        maxDrawdown: 10,
        autoReinvest: true,
      },
      performance: {
        totalTrades: 24,
        winningTrades: 18,
        losingTrades: 6,
        avgProfit: 12.5,
        avgLoss: -3.2,
        maxDrawdown: 8.5,
        sharpeRatio: 1.8,
      },
    },
    {
      id: 'bot-2',
      name: 'Scalping Beast',
      status: 'paused',
      strategy: 'Scalping',
      riskLevel: 'high',
      investedAmount: 500,
      currentValue: 485,
      totalReturn: -15,
      returnPercent: -3,
      dailyReturn: -1.2,
      tradesCount: 156,
      winRate: 68,
      lastTrade: '1 hour ago',
      createdAt: '2024-01-20',
      tradingPairs: ['DOGE/USDT', 'SHIB/USDT', 'PEPE/USDT'],
      config: {
        stopLoss: 2,
        takeProfit: 8,
        maxDrawdown: 15,
        autoReinvest: false,
      },
      performance: {
        totalTrades: 156,
        winningTrades: 106,
        losingTrades: 50,
        avgProfit: 4.2,
        avgLoss: -2.1,
        maxDrawdown: 12.3,
        sharpeRatio: 0.9,
      },
    },
  ]);

  const [selectedBot, setSelectedBot] = useState<TradingBot | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'paused': return 'text-yellow-600 bg-yellow-100';
      case 'stopped': return 'text-gray-600 bg-gray-100';
      case 'error': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-blue-600 bg-blue-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const handleBotAction = (botId: string, action: 'start' | 'pause' | 'stop') => {
    setBots(prev => prev.map(bot => 
      bot.id === botId 
        ? { ...bot, status: action === 'start' ? 'active' : action as any }
        : bot
    ));
  };

  const totalInvested = bots.reduce((sum, bot) => sum + bot.investedAmount, 0);
  const totalValue = bots.reduce((sum, bot) => sum + bot.currentValue, 0);
  const totalReturn = totalValue - totalInvested;
  const totalReturnPercent = totalInvested > 0 ? (totalReturn / totalInvested) * 100 : 0;

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Bot Management</h1>
            <p className="text-gray-600 mt-1">Monitor and control your trading bots</p>
          </div>
          <button
            onClick={onSetupBot}
            className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-105"
          >
            <FontAwesomeIcon icon={ICON_NAMES.PLUS} className="mr-2" />
            Setup New Bot
          </button>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-2xl p-6 shadow-lg">
            <div className="flex items-center justify-between mb-4">
              <FontAwesomeIcon icon={ICON_NAMES.ROBOT} className="text-2xl text-purple-600" />
              <span className="text-sm text-gray-500">Active Bots</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {bots.filter(bot => bot.status === 'active').length}
            </div>
            <div className="text-sm text-gray-500">
              of {bots.length} total
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg">
            <div className="flex items-center justify-between mb-4">
              <FontAwesomeIcon icon={ICON_NAMES.WALLET} className="text-2xl text-blue-600" />
              <span className="text-sm text-gray-500">Total Invested</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {formatCurrency(totalInvested)}
            </div>
            <div className="text-sm text-gray-500">
              Across all bots
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg">
            <div className="flex items-center justify-between mb-4">
              <FontAwesomeIcon icon={ICON_NAMES.CHART_LINE} className="text-2xl text-green-600" />
              <span className="text-sm text-gray-500">Total Value</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {formatCurrency(totalValue)}
            </div>
            <div className={`text-sm ${totalReturn >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {totalReturn >= 0 ? '+' : ''}{formatCurrency(totalReturn)}
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg">
            <div className="flex items-center justify-between mb-4">
              <FontAwesomeIcon icon={ICON_NAMES.PERCENTAGE} className="text-2xl text-pink-600" />
              <span className="text-sm text-gray-500">Total Return</span>
            </div>
            <div className={`text-2xl font-bold ${totalReturnPercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {totalReturnPercent >= 0 ? '+' : ''}{formatPercentage(totalReturnPercent)}
            </div>
            <div className="text-sm text-gray-500">
              Overall performance
            </div>
          </div>
        </div>

        {/* Bots List */}
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">Your Trading Bots</h2>
          </div>

          <div className="divide-y divide-gray-200">
            {bots.map((bot) => (
              <div key={bot.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                      <FontAwesomeIcon icon={ICON_NAMES.ROBOT} className="text-white text-xl" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{bot.name}</h3>
                      <div className="flex items-center space-x-3 mt-1">
                        <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(bot.status)}`}>
                          {bot.status.toUpperCase()}
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs ${getRiskColor(bot.riskLevel)}`}>
                          {bot.riskLevel.toUpperCase()} RISK
                        </span>
                        <span className="text-sm text-gray-500">{bot.strategy}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-8">
                    <div className="text-right">
                      <div className="text-sm text-gray-500">Invested</div>
                      <div className="font-semibold">{formatCurrency(bot.investedAmount)}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-500">Current Value</div>
                      <div className="font-semibold">{formatCurrency(bot.currentValue)}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-500">Return</div>
                      <div className={`font-semibold ${bot.returnPercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {bot.returnPercent >= 0 ? '+' : ''}{formatPercentage(bot.returnPercent)}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-500">Win Rate</div>
                      <div className="font-semibold">{formatPercentage(bot.winRate)}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-500">Last Trade</div>
                      <div className="text-sm text-gray-600">{bot.lastTrade}</div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {bot.status === 'active' ? (
                        <button
                          onClick={() => handleBotAction(bot.id, 'pause')}
                          className="p-2 text-yellow-600 hover:bg-yellow-100 rounded-lg transition-colors"
                          title="Pause Bot"
                        >
                          <FontAwesomeIcon icon={ICON_NAMES.PAUSE} />
                        </button>
                      ) : (
                        <button
                          onClick={() => handleBotAction(bot.id, 'start')}
                          className="p-2 text-green-600 hover:bg-green-100 rounded-lg transition-colors"
                          title="Start Bot"
                        >
                          <FontAwesomeIcon icon={ICON_NAMES.PLAY} />
                        </button>
                      )}
                      <button
                        onClick={() => handleBotAction(bot.id, 'stop')}
                        className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors"
                        title="Stop Bot"
                      >
                        <FontAwesomeIcon icon={ICON_NAMES.STOP} />
                      </button>
                      <button
                        onClick={() => {
                          setSelectedBot(bot);
                          setShowDetails(true);
                        }}
                        className="p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors"
                        title="View Details"
                      >
                        <FontAwesomeIcon icon={ICON_NAMES.EYE} />
                      </button>
                      <button
                        onClick={() => onEditBot(bot)}
                        className="p-2 text-purple-600 hover:bg-purple-100 rounded-lg transition-colors"
                        title="Edit Bot"
                      >
                        <FontAwesomeIcon icon={ICON_NAMES.EDIT} />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Bot Details Modal */}
        {showDetails && selectedBot && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <div className="flex justify-between items-center">
                  <h2 className="text-2xl font-bold text-gray-900">{selectedBot.name}</h2>
                  <button
                    onClick={() => setShowDetails(false)}
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
                  >
                    <FontAwesomeIcon icon={ICON_NAMES.TIMES} className="text-xl" />
                  </button>
                </div>
              </div>

              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Performance Metrics */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900">Performance Metrics</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-gray-50 rounded-xl p-4">
                        <div className="text-sm text-gray-500">Total Trades</div>
                        <div className="text-xl font-bold text-gray-900">{selectedBot.performance.totalTrades}</div>
                      </div>
                      <div className="bg-gray-50 rounded-xl p-4">
                        <div className="text-sm text-gray-500">Win Rate</div>
                        <div className="text-xl font-bold text-green-600">
                          {formatPercentage((selectedBot.performance.winningTrades / selectedBot.performance.totalTrades) * 100)}
                        </div>
                      </div>
                      <div className="bg-gray-50 rounded-xl p-4">
                        <div className="text-sm text-gray-500">Avg Profit</div>
                        <div className="text-xl font-bold text-green-600">
                          {formatPercentage(selectedBot.performance.avgProfit)}
                        </div>
                      </div>
                      <div className="bg-gray-50 rounded-xl p-4">
                        <div className="text-sm text-gray-500">Max Drawdown</div>
                        <div className="text-xl font-bold text-red-600">
                          -{formatPercentage(selectedBot.performance.maxDrawdown)}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Configuration */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900">Configuration</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center p-3 bg-gray-50 rounded-xl">
                        <span className="text-gray-600">Stop Loss</span>
                        <span className="font-semibold">{formatPercentage(selectedBot.config.stopLoss)}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-gray-50 rounded-xl">
                        <span className="text-gray-600">Take Profit</span>
                        <span className="font-semibold">{formatPercentage(selectedBot.config.takeProfit)}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-gray-50 rounded-xl">
                        <span className="text-gray-600">Max Drawdown</span>
                        <span className="font-semibold">{formatPercentage(selectedBot.config.maxDrawdown)}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-gray-50 rounded-xl">
                        <span className="text-gray-600">Auto Reinvest</span>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          selectedBot.config.autoReinvest ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'
                        }`}>
                          {selectedBot.config.autoReinvest ? 'Enabled' : 'Disabled'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Trading Pairs */}
                <div className="mt-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Trading Pairs</h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedBot.tradingPairs.map((pair) => (
                      <span key={pair} className="px-3 py-1 bg-purple-100 text-purple-600 rounded-full text-sm font-medium">
                        {pair}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
