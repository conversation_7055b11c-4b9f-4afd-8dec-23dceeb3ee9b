/**
 * ==============================================
 * PORTFOLIO MANAGEMENT PAGE
 * ==============================================
 * 
 * This component displays the user's portfolio with investment tracking,
 * bot performance, trade history, and real-time updates.
 */

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';
import { Portfolio as PortfolioType, BotInvestment, User } from '../types';
import { formatCurrency, formatPercentage, formatDate, getRelativeTime } from '../lib/utils';

interface PortfolioProps {
  portfolio: PortfolioType | null;
  botInvestments: BotInvestment[];
  user: User;
  onBotClick: (botId: string) => void;
}

export const Portfolio: React.FC<PortfolioProps> = ({
  portfolio,
  botInvestments,
  user,
  onBotClick
}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'investments' | 'trades'>('overview');
  const [trades] = useState([]); // TODO: Load real trades from database

  if (!portfolio) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <FontAwesomeIcon icon={ICON_NAMES.LOADING} className="text-4xl text-purple-500 animate-spin mb-4" />
          <p className="text-gray-600">Loading portfolio...</p>
        </div>
      </div>
    );
  }

  const totalProfitLoss = portfolio.total_profit_loss;
  const profitLossPercentage = portfolio.profit_loss_percentage;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-black text-gray-900 mb-2">Portfolio</h1>
        <p className="text-gray-600">Track your investments and trading performance</p>
      </div>

      {/* Portfolio Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="glass-effect rounded-2xl p-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-gray-500 text-sm">Total Value</span>
            <FontAwesomeIcon icon={ICON_NAMES.WALLET} className="text-purple-500" />
          </div>
          <div className="text-2xl font-black text-gray-900">
            {formatCurrency(portfolio.total_value)}
          </div>
        </div>

        <div className="glass-effect rounded-2xl p-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-gray-500 text-sm">Total Invested</span>
            <FontAwesomeIcon icon={ICON_NAMES.COINS} className="text-blue-500" />
          </div>
          <div className="text-2xl font-black text-gray-900">
            {formatCurrency(portfolio.total_invested)}
          </div>
        </div>

        <div className="glass-effect rounded-2xl p-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-gray-500 text-sm">Profit/Loss</span>
            <FontAwesomeIcon 
              icon={totalProfitLoss >= 0 ? ICON_NAMES.TRENDING_UP : ICON_NAMES.TRENDING_DOWN} 
              className={totalProfitLoss >= 0 ? 'text-green-500' : 'text-red-500'} 
            />
          </div>
          <div className={`text-2xl font-black ${totalProfitLoss >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {totalProfitLoss >= 0 ? '+' : ''}{formatCurrency(totalProfitLoss)}
          </div>
          <div className={`text-sm ${totalProfitLoss >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {formatPercentage(profitLossPercentage)}
          </div>
        </div>

        <div className="glass-effect rounded-2xl p-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-gray-500 text-sm">Active Bots</span>
            <FontAwesomeIcon icon={ICON_NAMES.ROBOT} className="text-orange-500" />
          </div>
          <div className="text-2xl font-black text-gray-900">
            {botInvestments.filter(inv => inv.status === 'active').length}
          </div>
        </div>
      </div>

      {/* Wallet Balance */}
      <div className="glass-effect rounded-2xl p-6">
        <h3 className="text-lg font-bold text-gray-900 mb-4">Wallet Balance</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gray-50 rounded-xl p-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-600 font-medium">USD</span>
              <FontAwesomeIcon icon={ICON_NAMES.COINS} className="text-green-500" />
            </div>
            <div className="text-xl font-bold text-gray-900 mt-2">
              {formatCurrency(portfolio.wallet_balance.usd)}
            </div>
          </div>
          <div className="bg-gray-50 rounded-xl p-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-600 font-medium">ETH</span>
              <FontAwesomeIcon icon={ICON_NAMES.ETHEREUM} className="text-blue-500" />
            </div>
            <div className="text-xl font-bold text-gray-900 mt-2">
              Ξ{portfolio.wallet_balance.eth.toFixed(4)}
            </div>
          </div>
          <div className="bg-gray-50 rounded-xl p-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-600 font-medium">BTC</span>
              <FontAwesomeIcon icon={ICON_NAMES.BITCOIN} className="text-orange-500" />
            </div>
            <div className="text-xl font-bold text-gray-900 mt-2">
              ₿{portfolio.wallet_balance.btc.toFixed(6)}
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="glass-effect rounded-2xl p-6">
        <div className="flex space-x-1 mb-6">
          {[
            { id: 'overview', label: 'Overview', icon: ICON_NAMES.CHART },
            { id: 'investments', label: 'Investments', icon: ICON_NAMES.ROBOT },
            { id: 'trades', label: 'Trade History', icon: ICON_NAMES.EXCHANGE },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-xl font-medium transition-all-smooth ${
                activeTab === tab.id
                  ? 'bg-purple-500 text-white'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <FontAwesomeIcon icon={tab.icon} />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <div>
              <h4 className="text-lg font-bold text-gray-900 mb-4">Performance Summary</h4>
              <div className="bg-gray-50 rounded-xl p-4">
                <p className="text-gray-600 text-center">
                  Performance charts and analytics will be displayed here.
                  Integration with Chart.js for interactive visualizations.
                </p>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'investments' && (
          <div className="space-y-4">
            <h4 className="text-lg font-bold text-gray-900">Active Investments</h4>
            {botInvestments.length > 0 ? (
              botInvestments.map((investment) => (
                <div
                  key={investment.id}
                  className="border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all-smooth cursor-pointer"
                  onClick={() => onBotClick(investment.bot_id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                        <FontAwesomeIcon icon={ICON_NAMES.ROBOT} className="text-white" />
                      </div>
                      <div>
                        <h5 className="font-bold text-gray-900">{investment.bot.name}</h5>
                        <p className="text-sm text-gray-600">
                          Started {formatDate(investment.start_date)}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-gray-900">
                        {formatCurrency(investment.current_value)}
                      </div>
                      <div className={`text-sm font-medium ${
                        investment.profit_loss >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {investment.profit_loss >= 0 ? '+' : ''}{formatCurrency(investment.profit_loss)}
                        ({formatPercentage(investment.profit_loss_percentage)})
                      </div>
                    </div>
                  </div>
                  <div className="mt-4 pt-4 border-t border-gray-100">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Invested: {formatCurrency(investment.invested_amount)}</span>
                      <span className={`font-medium px-2 py-1 rounded-full text-xs ${
                        investment.status === 'active' ? 'bg-green-100 text-green-600' :
                        investment.status === 'paused' ? 'bg-yellow-100 text-yellow-600' :
                        'bg-gray-100 text-gray-600'
                      }`}>
                        {investment.status.toUpperCase()}
                      </span>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <FontAwesomeIcon icon={ICON_NAMES.ROBOT} className="text-4xl text-gray-400 mb-4" />
                <p className="text-gray-500">No active investments</p>
                <p className="text-gray-400 text-sm">Start investing in trading bots to see them here</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'trades' && (
          <div className="space-y-4">
            <h4 className="text-lg font-bold text-gray-900">Recent Trades</h4>
            {trades.length > 0 ? (
              trades.map((trade) => (
                <div key={trade.id} className="border border-gray-200 rounded-xl p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${
                        trade.type === 'buy' ? 'bg-green-100' : 'bg-red-100'
                      }`}>
                        <FontAwesomeIcon 
                          icon={trade.type === 'buy' ? ICON_NAMES.ARROW_UP : ICON_NAMES.ARROW_DOWN}
                          className={trade.type === 'buy' ? 'text-green-600' : 'text-red-600'}
                        />
                      </div>
                      <div>
                        <div className="font-semibold text-gray-900">
                          {trade.type.toUpperCase()} {trade.coin_symbol}
                        </div>
                        <div className="text-sm text-gray-600">
                          {formatDate(trade.executed_at)} • {getRelativeTime(trade.executed_at)}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-gray-900">
                        {formatCurrency(trade.total_value)}
                      </div>
                      <div className="text-sm text-gray-600">
                        {trade.amount.toLocaleString()} @ {formatCurrency(trade.price)}
                      </div>
                      {trade.profit_loss && (
                        <div className={`text-sm font-medium ${
                          trade.profit_loss >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {trade.profit_loss >= 0 ? '+' : ''}{formatCurrency(trade.profit_loss)}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <FontAwesomeIcon icon={ICON_NAMES.EXCHANGE} className="text-4xl text-gray-400 mb-4" />
                <p className="text-gray-500">No trades yet</p>
                <p className="text-gray-400 text-sm">Your trading history will appear here</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
