/**
 * ==============================================
 * BOT SETUP AND CONFIGURATION PAGE
 * ==============================================
 * 
 * Complete bot setup flow with configuration, risk settings,
 * trading parameters, and activation controls.
 */

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';
import { colors, components, animations } from '../lib/designSystem';
import { formatCurrency, formatPercentage } from '../lib/utils';

interface BotConfig {
  id: string;
  name: string;
  strategy: string;
  riskLevel: 'low' | 'medium' | 'high';
  investmentAmount: number;
  maxDrawdown: number;
  stopLoss: number;
  takeProfit: number;
  tradingPairs: string[];
  autoReinvest: boolean;
  maxDailyTrades: number;
  tradingHours: {
    start: string;
    end: string;
    timezone: string;
  };
  notifications: {
    trades: boolean;
    profits: boolean;
    losses: boolean;
    dailyReports: boolean;
  };
}

interface BotSetupProps {
  selectedBot: any;
  onComplete: (config: BotConfig) => void;
  onCancel: () => void;
}

export const BotSetup: React.FC<BotSetupProps> = ({
  selectedBot,
  onComplete,
  onCancel,
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [config, setConfig] = useState<BotConfig>({
    id: selectedBot?.id || '',
    name: selectedBot?.name || '',
    strategy: selectedBot?.strategy || 'DCA',
    riskLevel: selectedBot?.riskLevel || 'medium',
    investmentAmount: selectedBot?.minInvestment || 100,
    maxDrawdown: 10,
    stopLoss: 5,
    takeProfit: 15,
    tradingPairs: ['BTC/USDT', 'ETH/USDT'],
    autoReinvest: true,
    maxDailyTrades: 10,
    tradingHours: {
      start: '00:00',
      end: '23:59',
      timezone: 'UTC',
    },
    notifications: {
      trades: true,
      profits: true,
      losses: true,
      dailyReports: true,
    },
  });

  const steps = [
    { id: 1, title: 'Basic Settings', icon: ICON_NAMES.SETTINGS },
    { id: 2, title: 'Risk Management', icon: ICON_NAMES.SHIELD },
    { id: 3, title: 'Trading Pairs', icon: ICON_NAMES.CHART_LINE },
    { id: 4, title: 'Schedule & Notifications', icon: ICON_NAMES.CLOCK },
    { id: 5, title: 'Review & Activate', icon: ICON_NAMES.CHECK_CIRCLE },
  ];

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    onComplete(config);
  };

  const updateConfig = (updates: Partial<BotConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-blue-600 bg-blue-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Bot Name
              </label>
              <input
                type="text"
                value={config.name}
                onChange={(e) => updateConfig({ name: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                placeholder="Enter bot name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Investment Amount (USDT)
              </label>
              <input
                type="number"
                value={config.investmentAmount}
                onChange={(e) => updateConfig({ investmentAmount: Number(e.target.value) })}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                min={selectedBot?.minInvestment || 100}
                max={selectedBot?.maxInvestment || 10000}
              />
              <p className="text-sm text-gray-500 mt-1">
                Min: {formatCurrency(selectedBot?.minInvestment || 100)} - Max: {formatCurrency(selectedBot?.maxInvestment || 10000)}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Risk Level
              </label>
              <div className="grid grid-cols-3 gap-3">
                {['low', 'medium', 'high'].map((risk) => (
                  <button
                    key={risk}
                    onClick={() => updateConfig({ riskLevel: risk as any })}
                    className={`p-4 rounded-xl border-2 transition-all ${
                      config.riskLevel === risk
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className={`text-xs px-2 py-1 rounded-full ${getRiskColor(risk)} mb-2`}>
                      {risk.toUpperCase()}
                    </div>
                    <div className="text-sm font-medium capitalize">{risk} Risk</div>
                  </button>
                ))}
              </div>
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
              <div className="flex items-center space-x-3">
                <FontAwesomeIcon icon={ICON_NAMES.REFRESH} className="text-purple-600" />
                <div>
                  <div className="font-medium">Auto-Reinvest Profits</div>
                  <div className="text-sm text-gray-500">Automatically reinvest profits to compound returns</div>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={config.autoReinvest}
                  onChange={(e) => updateConfig({ autoReinvest: e.target.checked })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
              </label>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Maximum Drawdown (%)
              </label>
              <input
                type="number"
                value={config.maxDrawdown}
                onChange={(e) => updateConfig({ maxDrawdown: Number(e.target.value) })}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                min="1"
                max="50"
                step="0.1"
              />
              <p className="text-sm text-gray-500 mt-1">
                Maximum portfolio loss before bot stops trading
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Stop Loss (%)
              </label>
              <input
                type="number"
                value={config.stopLoss}
                onChange={(e) => updateConfig({ stopLoss: Number(e.target.value) })}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                min="0.1"
                max="20"
                step="0.1"
              />
              <p className="text-sm text-gray-500 mt-1">
                Automatic sell when position loses this percentage
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Take Profit (%)
              </label>
              <input
                type="number"
                value={config.takeProfit}
                onChange={(e) => updateConfig({ takeProfit: Number(e.target.value) })}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                min="1"
                max="100"
                step="0.1"
              />
              <p className="text-sm text-gray-500 mt-1">
                Automatic sell when position gains this percentage
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Maximum Daily Trades
              </label>
              <input
                type="number"
                value={config.maxDailyTrades}
                onChange={(e) => updateConfig({ maxDailyTrades: Number(e.target.value) })}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                min="1"
                max="100"
              />
              <p className="text-sm text-gray-500 mt-1">
                Limit the number of trades per day to manage risk
              </p>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-4">
                Select Trading Pairs
              </label>
              <div className="grid grid-cols-2 gap-3">
                {[
                  'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT',
                  'SOL/USDT', 'DOGE/USDT', 'SHIB/USDT', 'PEPE/USDT',
                  'MATIC/USDT', 'AVAX/USDT', 'DOT/USDT', 'LINK/USDT'
                ].map((pair) => (
                  <label key={pair} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-xl hover:bg-gray-50 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={config.tradingPairs.includes(pair)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          updateConfig({ tradingPairs: [...config.tradingPairs, pair] });
                        } else {
                          updateConfig({ tradingPairs: config.tradingPairs.filter(p => p !== pair) });
                        }
                      }}
                      className="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500"
                    />
                    <span className="font-medium">{pair}</span>
                  </label>
                ))}
              </div>
              <p className="text-sm text-gray-500 mt-2">
                Selected: {config.tradingPairs.length} pairs
              </p>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-4">
                Trading Hours
              </label>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Start Time</label>
                  <input
                    type="time"
                    value={config.tradingHours.start}
                    onChange={(e) => updateConfig({
                      tradingHours: { ...config.tradingHours, start: e.target.value }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-500 mb-1">End Time</label>
                  <input
                    type="time"
                    value={config.tradingHours.end}
                    onChange={(e) => updateConfig({
                      tradingHours: { ...config.tradingHours, end: e.target.value }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-4">
                Notifications
              </label>
              <div className="space-y-3">
                {[
                  { key: 'trades', label: 'Trade Executions', desc: 'Get notified when trades are executed' },
                  { key: 'profits', label: 'Profit Alerts', desc: 'Notifications for profitable trades' },
                  { key: 'losses', label: 'Loss Alerts', desc: 'Notifications for losing trades' },
                  { key: 'dailyReports', label: 'Daily Reports', desc: 'Daily performance summaries' },
                ].map((notification) => (
                  <div key={notification.key} className="flex items-center justify-between p-3 border border-gray-200 rounded-xl">
                    <div>
                      <div className="font-medium">{notification.label}</div>
                      <div className="text-sm text-gray-500">{notification.desc}</div>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={config.notifications[notification.key as keyof typeof config.notifications]}
                        onChange={(e) => updateConfig({
                          notifications: {
                            ...config.notifications,
                            [notification.key]: e.target.checked
                          }
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      case 5:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <FontAwesomeIcon icon={ICON_NAMES.ROBOT} className="text-6xl text-purple-600 mb-4" />
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Ready to Launch!</h3>
              <p className="text-gray-600">Review your bot configuration and start trading</p>
            </div>

            <div className="bg-gray-50 rounded-xl p-6 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-gray-500">Bot Name</div>
                  <div className="font-medium">{config.name}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Investment</div>
                  <div className="font-medium">{formatCurrency(config.investmentAmount)}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Risk Level</div>
                  <div className={`inline-block px-2 py-1 rounded-full text-xs ${getRiskColor(config.riskLevel)}`}>
                    {config.riskLevel.toUpperCase()}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Trading Pairs</div>
                  <div className="font-medium">{config.tradingPairs.length} pairs</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Stop Loss</div>
                  <div className="font-medium">{formatPercentage(config.stopLoss)}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Take Profit</div>
                  <div className="font-medium">{formatPercentage(config.takeProfit)}</div>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
              <div className="flex items-start space-x-3">
                <FontAwesomeIcon icon={ICON_NAMES.WARNING} className="text-yellow-600 mt-1" />
                <div>
                  <div className="font-medium text-yellow-800">Important Notice</div>
                  <div className="text-sm text-yellow-700 mt-1">
                    Trading bots involve risk. Past performance does not guarantee future results. 
                    Only invest what you can afford to lose.
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Bot Setup</h1>
          <p className="text-gray-600">Configure your trading bot for optimal performance</p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  currentStep >= step.id
                    ? 'bg-purple-600 border-purple-600 text-white'
                    : 'bg-white border-gray-300 text-gray-400'
                }`}>
                  {currentStep > step.id ? (
                    <FontAwesomeIcon icon={ICON_NAMES.CHECK} className="text-sm" />
                  ) : (
                    <FontAwesomeIcon icon={step.icon} className="text-sm" />
                  )}
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 mx-2 ${
                    currentStep > step.id ? 'bg-purple-600' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-between mt-2">
            {steps.map((step) => (
              <div key={step.id} className="text-xs text-gray-500 text-center" style={{ width: '80px' }}>
                {step.title}
              </div>
            ))}
          </div>
        </div>

        {/* Step Content */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">
            {steps[currentStep - 1]?.title}
          </h2>
          {renderStepContent()}
        </div>

        {/* Navigation */}
        <div className="flex justify-between">
          <button
            onClick={onCancel}
            className="px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <div className="flex space-x-3">
            {currentStep > 1 && (
              <button
                onClick={handlePrevious}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors"
              >
                Previous
              </button>
            )}
            {currentStep < steps.length ? (
              <button
                onClick={handleNext}
                className="px-6 py-3 bg-purple-600 text-white rounded-xl hover:bg-purple-700 transition-colors"
              >
                Next
              </button>
            ) : (
              <button
                onClick={handleComplete}
                className="px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-105"
              >
                <FontAwesomeIcon icon={ICON_NAMES.ROCKET} className="mr-2" />
                Launch Bot
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
