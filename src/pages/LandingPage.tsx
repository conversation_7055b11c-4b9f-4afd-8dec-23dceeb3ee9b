/**
 * ==============================================
 * LANDING PAGE COMPONENT
 * ==============================================
 * 
 * This component renders the main landing page for the crypto meme coin
 * bot trading platform. Includes hero section, featured bots, how it works,
 * and trust signals as specified in the PRD.
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';
import { TradingBot } from '../types';
import { formatPercentage, formatCurrency } from '../lib/utils';

// Static featured bots for landing page
const FEATURED_BOTS: TradingBot[] = [
  {
    id: 'featured-1',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    description: 'Specialized in Shiba Inu and similar meme coins with momentum trading strategy',
    strategy: {
      type: 'momentum',
      parameters: { momentum_threshold: 0.05, volume_threshold: 1000000 },
      stop_loss_percentage: 5,
      take_profit_percentage: 15,
      max_trades_per_day: 10
    },
    target_coins: ['SHIB', 'DOGE', 'PEPE'],
    risk_level: 'medium',
    min_investment: 100,
    max_investment: 10000,
    performance: {
      total_return_percentage: 24.5,
      win_rate: 68,
      total_trades: 156,
      profitable_trades: 106,
      average_trade_duration: 4.2,
      max_drawdown: 8.3,
      sharpe_ratio: 1.8,
      last_30_days_return: 12.3,
      last_7_days_return: 3.7
    },
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'featured-2',
    name: 'Arbitrage Master',
    description: 'Cross-exchange arbitrage opportunities for maximum profit with minimal risk',
    strategy: {
      type: 'arbitrage',
      parameters: { min_spread: 0.02, max_slippage: 0.01 },
      stop_loss_percentage: 2,
      take_profit_percentage: 5,
      max_trades_per_day: 50
    },
    target_coins: ['DOGE', 'SHIB', 'FLOKI', 'BONK'],
    risk_level: 'low',
    min_investment: 500,
    max_investment: 50000,
    performance: {
      total_return_percentage: 18.2,
      win_rate: 89,
      total_trades: 342,
      profitable_trades: 304,
      average_trade_duration: 0.8,
      max_drawdown: 3.1,
      sharpe_ratio: 2.4,
      last_30_days_return: 8.9,
      last_7_days_return: 2.1
    },
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'featured-3',
    name: 'Scalping Pro',
    description: 'High-frequency trading bot for quick profits on small price movements',
    strategy: {
      type: 'scalping',
      parameters: { price_threshold: 0.01, volume_threshold: 500000 },
      stop_loss_percentage: 1,
      take_profit_percentage: 2,
      max_trades_per_day: 200
    },
    target_coins: ['PEPE', 'BONK', 'WIF', 'MEME'],
    risk_level: 'high',
    min_investment: 1000,
    max_investment: 25000,
    performance: {
      total_return_percentage: 31.7,
      win_rate: 72,
      total_trades: 1247,
      profitable_trades: 897,
      average_trade_duration: 0.3,
      max_drawdown: 12.5,
      sharpe_ratio: 2.1,
      last_30_days_return: 15.8,
      last_7_days_return: 4.2
    },
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
];

export const LandingPage: React.FC = () => {
  const navigate = useNavigate();
  const [featuredBots] = useState<TradingBot[]>(FEATURED_BOTS);

  const handleGetStarted = () => {
    navigate('/signup');
  };

  const handleLearnMore = () => {
    // Scroll to how it works section
    document.getElementById('how-it-works')?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900">
      {/* Navigation Header */}
      <nav className="glass-effect border-b border-purple-500/20 px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <FontAwesomeIcon icon={ICON_NAMES.ROCKET} className="text-purple-400 text-2xl" />
            <span className="text-2xl font-black text-white">MemeBot Pro</span>
          </div>
          <div className="hidden md:flex items-center space-x-8">
            <a href="#features" className="text-gray-300 hover:text-purple-400 font-medium transition-colors">Features</a>
            <a href="#how-it-works" className="text-gray-300 hover:text-purple-400 font-medium transition-colors">How It Works</a>
            <a href="#bots" className="text-gray-300 hover:text-purple-400 font-medium transition-colors">Bots</a>
            <a href="#faq" className="text-gray-300 hover:text-purple-400 font-medium transition-colors">FAQ</a>
          </div>
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/login')}
              className="text-gray-300 hover:text-purple-400 font-medium transition-colors px-4 py-2 rounded-lg hover:bg-purple-500/10"
            >
              Sign In
            </button>
            <button
              onClick={handleGetStarted}
              className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold px-6 py-3 rounded-xl transition-all-smooth transform hover:scale-105"
            >
              Get Started
            </button>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative px-6 py-20 md:py-32">
        <div className="max-w-7xl mx-auto text-center">
          <div className="mb-8">
            <h1 className="text-5xl md:text-7xl font-black text-white mb-6 leading-tight">
              Let Bots Trade
              <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"> Memecoins </span>
              for You!
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Low-risk, high-reward trading with our AI-powered bots. 
              Start earning from meme coin volatility without the stress.
            </p>
          </div>

          <div className="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-6 mb-12">
            <button
              onClick={handleGetStarted}
              className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold px-8 py-4 rounded-2xl text-lg transition-all-smooth transform hover:scale-105 shadow-2xl"
            >
              <FontAwesomeIcon icon={ICON_NAMES.ROCKET} className="mr-2" />
              Start Trading Now
            </button>
            <button
              onClick={handleLearnMore}
              className="border-2 border-purple-400 text-purple-400 hover:bg-purple-400 hover:text-white font-bold px-8 py-4 rounded-2xl text-lg transition-all-smooth"
            >
              <FontAwesomeIcon icon={ICON_NAMES.INFO} className="mr-2" />
              Learn More
            </button>
          </div>

          {/* Trust Signals */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-black text-purple-400 mb-2">10,000+</div>
              <div className="text-gray-400 text-sm">Active Traders</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-black text-green-400 mb-2">$2.5M+</div>
              <div className="text-gray-400 text-sm">Total Profits</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-black text-blue-400 mb-2">24/7</div>
              <div className="text-gray-400 text-sm">Bot Trading</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-black text-yellow-400 mb-2">98%</div>
              <div className="text-gray-400 text-sm">Uptime</div>
            </div>
          </div>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 text-6xl opacity-20 animate-bounce-subtle">
          <FontAwesomeIcon icon={ICON_NAMES.BITCOIN} className="text-yellow-400" />
        </div>
        <div className="absolute top-40 right-20 text-4xl opacity-20 animate-bounce-subtle" style={{ animationDelay: '0.5s' }}>
          <FontAwesomeIcon icon={ICON_NAMES.ETHEREUM} className="text-blue-400" />
        </div>
        <div className="absolute bottom-20 left-20 text-5xl opacity-20 animate-bounce-subtle" style={{ animationDelay: '1s' }}>
          <FontAwesomeIcon icon={ICON_NAMES.ROCKET} className="text-purple-400" />
        </div>
      </section>

      {/* Featured Bots Section */}
      <section id="bots" className="px-6 py-20 bg-black/20">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-black text-white mb-6">
              Top Performing Bots
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Our AI-powered trading bots are designed to maximize profits while minimizing risks
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {featuredBots.map((bot, index) => (
              <div
                key={bot.id}
                className="glass-effect rounded-2xl p-6 hover:shadow-glow transition-all-smooth transform hover:scale-105"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-bold text-white">{bot.name}</h3>
                  <div className={`px-3 py-1 rounded-full text-xs font-bold ${
                    bot.risk_level === 'low' ? 'bg-green-500/20 text-green-400' :
                    bot.risk_level === 'medium' ? 'bg-yellow-500/20 text-yellow-400' :
                    'bg-red-500/20 text-red-400'
                  }`}>
                    {bot.risk_level.toUpperCase()} RISK
                  </div>
                </div>

                <p className="text-gray-300 text-sm mb-6">{bot.description}</p>

                <div className="space-y-3 mb-6">
                  <div className="flex justify-between">
                    <span className="text-gray-400">30-Day Return</span>
                    <span className="text-green-400 font-bold">
                      {formatPercentage(bot.performance.last_30_days_return)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Win Rate</span>
                    <span className="text-blue-400 font-bold">
                      {formatPercentage(bot.performance.win_rate, false)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Min Investment</span>
                    <span className="text-white font-bold">
                      {formatCurrency(bot.min_investment)}
                    </span>
                  </div>
                </div>

                <button
                  onClick={handleGetStarted}
                  className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-3 rounded-xl transition-all-smooth"
                >
                  <FontAwesomeIcon icon={ICON_NAMES.PLAY} className="mr-2" />
                  Start Trading
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="px-6 py-20">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-black text-white mb-6">
              How It Works
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Get started with automated meme coin trading in just 3 simple steps
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <FontAwesomeIcon icon={ICON_NAMES.USER} className="text-white text-2xl" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">1. Sign Up</h3>
              <p className="text-gray-300">
                Create your account with email or connect your crypto wallet. 
                Complete optional KYC for higher limits.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <FontAwesomeIcon icon={ICON_NAMES.WALLET} className="text-white text-2xl" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">2. Deposit</h3>
              <p className="text-gray-300">
                Fund your account with USDT, ETH, or other supported cryptocurrencies. 
                Start with as little as $100.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <FontAwesomeIcon icon={ICON_NAMES.ROBOT} className="text-white text-2xl" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">3. Trade</h3>
              <p className="text-gray-300">
                Choose your trading bot, set your investment amount, and let AI 
                handle the rest. Monitor profits 24/7.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black/40 border-t border-purple-500/20 px-6 py-12">
        <div className="max-w-7xl mx-auto text-center">
          <div className="flex items-center justify-center space-x-3 mb-6">
            <FontAwesomeIcon icon={ICON_NAMES.ROCKET} className="text-purple-400 text-2xl" />
            <span className="text-2xl font-black text-white">MemeBot Pro</span>
          </div>
          <p className="text-gray-400 mb-6">
            Automated meme coin trading for the modern investor
          </p>
          <div className="flex items-center justify-center space-x-8 text-sm text-gray-400">
            <a href="#" className="hover:text-purple-400 transition-colors">Terms of Service</a>
            <a href="#" className="hover:text-purple-400 transition-colors">Privacy Policy</a>
            <a href="#" className="hover:text-purple-400 transition-colors">Support</a>
            <a href="#" className="hover:text-purple-400 transition-colors">FAQ</a>
          </div>
        </div>
      </footer>
    </div>
  );
};
