/**
 * ==============================================
 * BOT MARKETPLACE PAGE
 * ==============================================
 * 
 * Complete bot marketplace where users can browse and invest in trading bots.
 * Features filtering, sorting, and detailed bot information.
 */

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';
import { colors, components, animations } from '../lib/designSystem';
import { cryptoIcons, botIllustrations, assetUtils } from '../lib/cryptoAssets';
import { formatCurrency, formatPercentage } from '../lib/utils';

interface TradingBot {
  id: string;
  name: string;
  description: string;
  riskLevel: 'low' | 'medium' | 'high';
  minInvestment: number;
  maxInvestment: number;
  expectedReturn: number;
  winRate: number;
  totalInvestors: number;
  totalFunds: number;
  supportedCoins: string[];
  performance30d: number;
  performance7d: number;
  isActive: boolean;
  capacity: number;
  used: number;
}

const TRADING_BOTS: TradingBot[] = [
  {
    id: '1',
    name: 'Conservative Growth',
    description: 'Steady, low-risk growth with focus on established meme coins',
    riskLevel: 'low',
    minInvestment: 50,
    maxInvestment: 10000,
    expectedReturn: 15,
    winRate: 78,
    totalInvestors: 1247,
    totalFunds: 2450000,
    supportedCoins: ['DOGE', 'SHIB', 'PEPE'],
    performance30d: 12.5,
    performance7d: 3.2,
    isActive: true,
    capacity: 5000000,
    used: 2450000,
  },
  {
    id: '2',
    name: 'Momentum Hunter',
    description: 'Captures trending meme coins with moderate risk approach',
    riskLevel: 'medium',
    minInvestment: 100,
    maxInvestment: 25000,
    expectedReturn: 35,
    winRate: 65,
    totalInvestors: 892,
    totalFunds: 1850000,
    supportedCoins: ['BONK', 'WIF', 'FLOKI', 'PEPE'],
    performance30d: 28.7,
    performance7d: 8.1,
    isActive: true,
    capacity: 3000000,
    used: 1850000,
  },
  {
    id: '3',
    name: 'Alpha Seeker',
    description: 'High-risk, high-reward strategy for maximum gains',
    riskLevel: 'high',
    minInvestment: 200,
    maxInvestment: 50000,
    expectedReturn: 75,
    winRate: 52,
    totalInvestors: 456,
    totalFunds: 980000,
    supportedCoins: ['WIF', 'BONK', 'FLOKI'],
    performance30d: 45.2,
    performance7d: 15.8,
    isActive: true,
    capacity: 2000000,
    used: 980000,
  },
];

interface BotMarketplaceProps {
  onInvestClick: (botId: string) => void;
}

export const BotMarketplace: React.FC<BotMarketplaceProps> = ({ onInvestClick }) => {
  const [selectedRisk, setSelectedRisk] = useState<'all' | 'low' | 'medium' | 'high'>('all');
  const [sortBy, setSortBy] = useState<'performance' | 'capacity' | 'investors'>('performance');

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-blue-600 bg-blue-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getRiskGradient = (risk: string) => {
    switch (risk) {
      case 'low': return 'from-green-500 to-emerald-500';
      case 'medium': return 'from-blue-500 to-cyan-500';
      case 'high': return 'from-red-500 to-pink-500';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  const filteredBots = TRADING_BOTS.filter(bot => 
    selectedRisk === 'all' || bot.riskLevel === selectedRisk
  );

  const sortedBots = [...filteredBots].sort((a, b) => {
    switch (sortBy) {
      case 'performance':
        return b.performance30d - a.performance30d;
      case 'capacity':
        return (b.capacity - b.used) - (a.capacity - a.used);
      case 'investors':
        return b.totalInvestors - a.totalInvestors;
      default:
        return 0;
    }
  });

  return (
    <div className="p-4 md:p-6 pb-32 md:pb-6 space-y-6">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl md:text-4xl font-black text-gray-900 mb-4">
          Trading Bot Marketplace
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Choose from our professionally managed trading bots and watch your investment grow automatically
        </p>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          {/* Risk Level Filter */}
          <div className="flex flex-wrap gap-2">
            <span className="text-sm font-medium text-gray-700 mr-2">Risk Level:</span>
            {['all', 'low', 'medium', 'high'].map((risk) => (
              <button
                key={risk}
                onClick={() => setSelectedRisk(risk as any)}
                className={`px-4 py-2 rounded-xl text-sm font-medium transition-all ${
                  selectedRisk === risk
                    ? 'bg-purple-500 text-white shadow-lg'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {risk.charAt(0).toUpperCase() + risk.slice(1)}
              </button>
            ))}
          </div>

          {/* Sort Options */}
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-gray-700">Sort by:</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-3 py-2 rounded-lg border border-gray-200 text-sm focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
            >
              <option value="performance">Performance</option>
              <option value="capacity">Available Capacity</option>
              <option value="investors">Total Investors</option>
            </select>
          </div>
        </div>
      </div>

      {/* Bot Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {sortedBots.map((bot) => (
          <div
            key={bot.id}
            className="bg-white rounded-3xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
          >
            {/* Bot Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <h3 className="text-xl font-bold text-gray-900">{bot.name}</h3>
                  <span className={`px-2 py-1 rounded-lg text-xs font-medium ${getRiskColor(bot.riskLevel)}`}>
                    {bot.riskLevel.toUpperCase()}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-3">{bot.description}</p>
              </div>
            </div>

            {/* Bot Illustration */}
            <div className="flex justify-center mb-4">
              <div 
                className="w-24 h-24"
                dangerouslySetInnerHTML={{ 
                  __html: assetUtils.getBotIllustration(bot.riskLevel) 
                }}
              />
            </div>

            {/* Performance Stats */}
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  +{formatPercentage(bot.performance30d)}
                </div>
                <div className="text-xs text-gray-500">30-day return</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-gray-900">
                  {formatPercentage(bot.winRate)}
                </div>
                <div className="text-xs text-gray-500">Win rate</div>
              </div>
            </div>

            {/* Supported Coins */}
            <div className="mb-4">
              <div className="text-xs font-medium text-gray-700 mb-2">Supported Coins:</div>
              <div className="flex flex-wrap gap-1">
                {bot.supportedCoins.map((coin) => (
                  <span
                    key={coin}
                    className="px-2 py-1 bg-gray-100 rounded-lg text-xs font-medium text-gray-700"
                  >
                    {coin}
                  </span>
                ))}
              </div>
            </div>

            {/* Investment Info */}
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Min Investment:</span>
                <span className="font-medium">{formatCurrency(bot.minInvestment)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Total Investors:</span>
                <span className="font-medium">{bot.totalInvestors.toLocaleString()}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Capacity:</span>
                <span className="font-medium">
                  {Math.round((bot.used / bot.capacity) * 100)}% filled
                </span>
              </div>
            </div>

            {/* Capacity Bar */}
            <div className="mb-4">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full bg-gradient-to-r ${getRiskGradient(bot.riskLevel)}`}
                  style={{ width: `${(bot.used / bot.capacity) * 100}%` }}
                />
              </div>
            </div>

            {/* Invest Button */}
            <button
              onClick={() => onInvestClick(bot.id)}
              className={`w-full py-3 rounded-2xl font-semibold text-white transition-all duration-200 transform hover:scale-105 bg-gradient-to-r ${getRiskGradient(bot.riskLevel)} hover:shadow-lg`}
            >
              <div className="flex items-center justify-center space-x-2">
                <FontAwesomeIcon icon={ICON_NAMES.INVEST} />
                <span>Invest Now</span>
              </div>
            </button>
          </div>
        ))}
      </div>

      {/* Bottom CTA */}
      <div className="text-center mt-12">
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-3xl p-8 border border-purple-100">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Ready to Start Growing Your Portfolio?
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Our AI-powered trading bots work 24/7 to maximize your returns while you sleep. 
            Start with as little as $50 and watch your investment grow.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <FontAwesomeIcon icon={ICON_NAMES.SHIELD} className="text-green-500" />
              <span>Secure & Regulated</span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <FontAwesomeIcon icon={ICON_NAMES.CLOCK} className="text-blue-500" />
              <span>24/7 Trading</span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <FontAwesomeIcon icon={ICON_NAMES.ROCKET} className="text-purple-500" />
              <span>AI-Powered</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
