/**
 * ==============================================
 * TRADING BOTS LISTING PAGE
 * ==============================================
 * 
 * This component displays all available trading bots with filtering,
 * search, and sorting capabilities. Users can browse and invest in bots.
 */

import React, { useState, useMemo } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';
import { TradingBot, FilterOptions } from '../types';
import { formatCurrency, formatPercentage, sortBotsByPerformance, filterBotsByRisk, filterBotsByReturn } from '../lib/utils';
import { getTradingBots } from '../lib/supabase';

interface BotsProps {
  onBotClick: (botId: string) => void;
  tradingBots: TradingBot[];
}

export const Bots: React.FC<BotsProps> = ({ onBotClick, tradingBots }) => {
  const [bots] = useState<TradingBot[]>(tradingBots);
  const [filters, setFilters] = useState<FilterOptions>({
    search: '',
    risk_level: undefined,
    min_return: undefined,
    sort_by: 'return',
    sort_order: 'desc',
  });

  // Filter and sort bots based on current filters
  const filteredBots = useMemo(() => {
    let result = [...bots];

    // Search filter
    if (filters.search) {
      result = result.filter(bot => 
        bot.name.toLowerCase().includes(filters.search!.toLowerCase()) ||
        bot.description.toLowerCase().includes(filters.search!.toLowerCase()) ||
        bot.target_coins.some(coin => coin.toLowerCase().includes(filters.search!.toLowerCase()))
      );
    }

    // Risk level filter
    if (filters.risk_level) {
      result = filterBotsByRisk(result, filters.risk_level);
    }

    // Minimum return filter
    if (filters.min_return) {
      result = filterBotsByReturn(result, filters.min_return);
    }

    // Sort
    if (filters.sort_by) {
      result = sortBotsByPerformance(result, filters.sort_by);
      if (filters.sort_order === 'asc') {
        result.reverse();
      }
    }

    return result;
  }, [bots, filters]);

  const handleFilterChange = (key: keyof FilterOptions, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      risk_level: undefined,
      min_return: undefined,
      sort_by: 'return',
      sort_order: 'desc',
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-black text-gray-900 mb-2">Trading Bots</h1>
          <p className="text-gray-600">Discover AI-powered trading bots for meme coins</p>
        </div>
        <div className="mt-4 md:mt-0">
          <div className="text-sm text-gray-500">
            Showing {filteredBots.length} of {bots.length} bots
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="glass-effect rounded-2xl p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <FontAwesomeIcon 
              icon={ICON_NAMES.SEARCH} 
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" 
            />
            <input
              type="text"
              placeholder="Search bots..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>

          {/* Risk Level Filter */}
          <select
            value={filters.risk_level || ''}
            onChange={(e) => handleFilterChange('risk_level', e.target.value || undefined)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            <option value="">All Risk Levels</option>
            <option value="low">Low Risk</option>
            <option value="medium">Medium Risk</option>
            <option value="high">High Risk</option>
          </select>

          {/* Sort By */}
          <select
            value={filters.sort_by}
            onChange={(e) => handleFilterChange('sort_by', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            <option value="return">Sort by Return</option>
            <option value="winRate">Sort by Win Rate</option>
            <option value="sharpe">Sort by Sharpe Ratio</option>
            <option value="trades">Sort by Total Trades</option>
          </select>

          {/* Clear Filters */}
          <button
            onClick={clearFilters}
            className="flex items-center justify-center px-4 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl transition-colors"
          >
            <FontAwesomeIcon icon={ICON_NAMES.FILTER} className="mr-2" />
            Clear Filters
          </button>
        </div>
      </div>

      {/* Bots Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredBots.map((bot, index) => (
          <div
            key={bot.id}
            className="glass-effect rounded-2xl p-6 hover:shadow-glow transition-all-smooth transform hover:scale-105 cursor-pointer animate-fade-in"
            style={{ animationDelay: `${index * 0.1}s` }}
            onClick={() => onBotClick(bot.id)}
          >
            {/* Bot Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                  <FontAwesomeIcon icon={ICON_NAMES.ROBOT} className="text-white text-lg" />
                </div>
                <div>
                  <h3 className="font-bold text-gray-900">{bot.name}</h3>
                  <div className="flex items-center space-x-2">
                    {bot.target_coins.slice(0, 3).map(coin => (
                      <span key={coin} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                        {coin}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
              <div className={`px-3 py-1 rounded-full text-xs font-bold ${
                bot.risk_level === 'low' ? 'bg-green-100 text-green-600' :
                bot.risk_level === 'medium' ? 'bg-yellow-100 text-yellow-600' :
                'bg-red-100 text-red-600'
              }`}>
                {bot.risk_level.toUpperCase()}
              </div>
            </div>

            {/* Description */}
            <p className="text-gray-600 text-sm mb-4 line-clamp-2">{bot.description}</p>

            {/* Performance Metrics */}
            <div className="space-y-3 mb-6">
              <div className="flex justify-between items-center">
                <span className="text-gray-500 text-sm">30-Day Return</span>
                <span className="font-bold text-green-600">
                  {formatPercentage(bot.performance.last_30_days_return)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-500 text-sm">Win Rate</span>
                <span className="font-bold text-blue-600">
                  {formatPercentage(bot.performance.win_rate, false)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-500 text-sm">Total Trades</span>
                <span className="font-bold text-gray-900">
                  {bot.performance.total_trades}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-500 text-sm">Min Investment</span>
                <span className="font-bold text-gray-900">
                  {formatCurrency(bot.min_investment)}
                </span>
              </div>
            </div>

            {/* Action Button */}
            <button className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-3 rounded-xl transition-all-smooth">
              <FontAwesomeIcon icon={ICON_NAMES.CHART} className="mr-2" />
              View Details
            </button>
          </div>
        ))}
      </div>

      {/* No Results */}
      {filteredBots.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">
            <FontAwesomeIcon icon={ICON_NAMES.SEARCH} />
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-2">No bots found</h3>
          <p className="text-gray-600 mb-4">Try adjusting your filters or search terms</p>
          <button
            onClick={clearFilters}
            className="bg-purple-500 hover:bg-purple-600 text-white font-bold px-6 py-3 rounded-xl transition-colors"
          >
            Clear All Filters
          </button>
        </div>
      )}
    </div>
  );
};
