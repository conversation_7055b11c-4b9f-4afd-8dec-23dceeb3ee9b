/**
 * ==============================================
 * ENHANCED DASHBOARD PAGE
 * ==============================================
 * 
 * Modern dashboard with TradingView widgets, portfolio overview,
 * big logo space, last login info, and lively animations.
 */

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';
import { themeClass, useTheme } from '../lib/ThemeContext';
import { formatCurrency, formatPercentage } from '../lib/utils';

interface DashboardProps {
  userBalances: any;
  botInvestments: any[];
  onNavigate: (page: string) => void;
}

export const EnhancedDashboard: React.FC<DashboardProps> = ({
  userBalances,
  botInvestments,
  onNavigate,
}) => {
  const { actualTheme } = useTheme();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [lastLogin] = useState(new Date(Date.now() - 2 * 60 * 60 * 1000)); // 2 hours ago

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(timer);
  }, []);

  // Calculate portfolio metrics
  const totalBalance = Object.values(userBalances).reduce((sum: number, balance: any) => sum + (typeof balance === 'number' ? balance * 50 : 0), 0);
  const totalBotInvestments = botInvestments.reduce((sum, bot) => sum + bot.currentValue, 0);
  const totalPortfolioValue = totalBalance + totalBotInvestments;
  const dailyChange = 1250; // Mock daily change
  const dailyChangePercent = (dailyChange / totalPortfolioValue) * 100;

  const activeBots = botInvestments.filter(bot => bot.isActive).length;
  const totalReturn = botInvestments.reduce((sum, bot) => sum + bot.totalReturn, 0);

  // Quick action items
  const quickActions = [
    {
      id: 'invest',
      title: 'Invest in Bot',
      description: 'Start automated trading',
      icon: ICON_NAMES.ROBOT,
      color: 'from-purple-500 to-pink-500',
      action: () => onNavigate('marketplace'),
    },
    {
      id: 'deposit',
      title: 'Add Funds',
      description: 'Deposit cryptocurrency',
      icon: ICON_NAMES.PLUS,
      color: 'from-green-500 to-emerald-500',
      action: () => onNavigate('deposit-withdraw'),
    },
    {
      id: 'portfolio',
      title: 'View Portfolio',
      description: 'Track your investments',
      icon: ICON_NAMES.CHART_PIE,
      color: 'from-blue-500 to-cyan-500',
      action: () => onNavigate('portfolio'),
    },
    {
      id: 'history',
      title: 'Transaction History',
      description: 'View all transactions',
      icon: ICON_NAMES.HISTORY,
      color: 'from-orange-500 to-red-500',
      action: () => onNavigate('transactions'),
    },
  ];

  // Market data (mock)
  const marketData = [
    { symbol: 'BTC', price: 43250, change: 2.5, volume: '28.5B' },
    { symbol: 'ETH', price: 2650, change: -1.2, volume: '15.2B' },
    { symbol: 'DOGE', price: 0.085, change: 8.7, volume: '2.1B' },
    { symbol: 'SHIB', price: 0.000024, change: 15.3, volume: '1.8B' },
  ];

  return (
    <div className={`min-h-screen ${themeClass.bgSecondary} p-4 md:p-6 pb-32 md:pb-6`}>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header Section with Logo and Welcome */}
        <div className={`${themeClass.bgCard} rounded-3xl p-8 shadow-lg border ${themeClass.borderPrimary}`}>
          <div className="flex flex-col lg:flex-row items-center justify-between space-y-6 lg:space-y-0">
            {/* Big Logo Section */}
            <div className="flex items-center space-x-6">
              <div className="w-20 h-20 bg-gradient-to-br from-purple-500 via-pink-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-xl">
                <FontAwesomeIcon icon={ICON_NAMES.ROBOT} className="text-white text-3xl" />
              </div>
              <div>
                <h1 className={`text-4xl font-black ${themeClass.textPrimary} mb-2`}>
                  Welcome to Morewise
                </h1>
                <p className={`text-lg ${themeClass.textSecondary}`}>
                  Your AI-Powered Trading Platform
                </p>
                <div className={`flex items-center space-x-4 mt-3 text-sm ${themeClass.textTertiary}`}>
                  <div className="flex items-center space-x-2">
                    <FontAwesomeIcon icon={ICON_NAMES.CLOCK} />
                    <span>Last login: {lastLogin.toLocaleDateString()} at {lastLogin.toLocaleTimeString()}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <FontAwesomeIcon icon={ICON_NAMES.GLOBE} />
                    <span>Online</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Current Time and Status */}
            <div className="text-center lg:text-right">
              <div className={`text-3xl font-bold ${themeClass.textPrimary} mb-1`}>
                {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </div>
              <div className={`text-sm ${themeClass.textSecondary} mb-3`}>
                {currentTime.toLocaleDateString([], { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </div>
              <div className="flex items-center justify-center lg:justify-end space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className={`text-sm font-medium ${themeClass.textSecondary}`}>
                  All Systems Operational
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Portfolio Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className={`${themeClass.bgCard} rounded-2xl p-6 shadow-lg border ${themeClass.borderPrimary} hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1`}>
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                <FontAwesomeIcon icon={ICON_NAMES.WALLET} className="text-white text-xl" />
              </div>
              <span className={`text-sm ${themeClass.textTertiary}`}>Total Portfolio</span>
            </div>
            <div className={`text-3xl font-bold ${themeClass.textPrimary} mb-2`}>
              {formatCurrency(totalPortfolioValue)}
            </div>
            <div className={`flex items-center space-x-2 ${dailyChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              <FontAwesomeIcon icon={dailyChange >= 0 ? ICON_NAMES.ARROW_UP : ICON_NAMES.ARROW_DOWN} />
              <span className="font-medium">
                {dailyChange >= 0 ? '+' : ''}{formatCurrency(dailyChange)} ({formatPercentage(dailyChangePercent)})
              </span>
            </div>
          </div>

          <div className={`${themeClass.bgCard} rounded-2xl p-6 shadow-lg border ${themeClass.borderPrimary} hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1`}>
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
                <FontAwesomeIcon icon={ICON_NAMES.ROBOT} className="text-white text-xl" />
              </div>
              <span className={`text-sm ${themeClass.textTertiary}`}>Active Bots</span>
            </div>
            <div className={`text-3xl font-bold ${themeClass.textPrimary} mb-2`}>
              {activeBots}
            </div>
            <div className={`text-sm ${themeClass.textSecondary}`}>
              Trading automatically
            </div>
          </div>

          <div className={`${themeClass.bgCard} rounded-2xl p-6 shadow-lg border ${themeClass.borderPrimary} hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1`}>
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                <FontAwesomeIcon icon={ICON_NAMES.CHART_LINE} className="text-white text-xl" />
              </div>
              <span className={`text-sm ${themeClass.textTertiary}`}>Total Returns</span>
            </div>
            <div className={`text-3xl font-bold ${totalReturn >= 0 ? 'text-green-600' : 'text-red-600'} mb-2`}>
              {totalReturn >= 0 ? '+' : ''}{formatCurrency(totalReturn)}
            </div>
            <div className={`text-sm ${themeClass.textSecondary}`}>
              From bot trading
            </div>
          </div>

          <div className={`${themeClass.bgCard} rounded-2xl p-6 shadow-lg border ${themeClass.borderPrimary} hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1`}>
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                <FontAwesomeIcon icon={ICON_NAMES.COINS} className="text-white text-xl" />
              </div>
              <span className={`text-sm ${themeClass.textTertiary}`}>Available Balance</span>
            </div>
            <div className={`text-3xl font-bold ${themeClass.textPrimary} mb-2`}>
              {formatCurrency(userBalances.USDT || 0)}
            </div>
            <div className={`text-sm ${themeClass.textSecondary}`}>
              USDT ready to invest
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className={`${themeClass.bgCard} rounded-2xl p-6 shadow-lg border ${themeClass.borderPrimary}`}>
          <h2 className={`text-2xl font-bold ${themeClass.textPrimary} mb-6`}>Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action) => (
              <button
                key={action.id}
                onClick={action.action}
                className={`p-6 rounded-xl bg-gradient-to-br ${action.color} text-white hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 hover:scale-105 group`}
              >
                <div className="flex flex-col items-center text-center space-y-3">
                  <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:bg-white/30 transition-colors">
                    <FontAwesomeIcon icon={action.icon} className="text-2xl" />
                  </div>
                  <div>
                    <div className="font-bold text-lg">{action.title}</div>
                    <div className="text-sm opacity-90">{action.description}</div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Market Overview and TradingView Widget */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Market Overview */}
          <div className={`${themeClass.bgCard} rounded-2xl p-6 shadow-lg border ${themeClass.borderPrimary}`}>
            <h3 className={`text-xl font-bold ${themeClass.textPrimary} mb-4`}>Market Overview</h3>
            <div className="space-y-4">
              {marketData.map((coin) => (
                <div key={coin.symbol} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                      <span className="text-white text-xs font-bold">{coin.symbol.slice(0, 2)}</span>
                    </div>
                    <div>
                      <div className={`font-medium ${themeClass.textPrimary}`}>{coin.symbol}</div>
                      <div className={`text-sm ${themeClass.textTertiary}`}>Vol: {coin.volume}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`font-bold ${themeClass.textPrimary}`}>
                      ${coin.price.toLocaleString()}
                    </div>
                    <div className={`text-sm ${coin.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {coin.change >= 0 ? '+' : ''}{coin.change}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* TradingView Widget Placeholder */}
          <div className={`lg:col-span-2 ${themeClass.bgCard} rounded-2xl p-6 shadow-lg border ${themeClass.borderPrimary}`}>
            <h3 className={`text-xl font-bold ${themeClass.textPrimary} mb-4`}>Market Chart</h3>
            <div className="relative h-64 bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl flex items-center justify-center">
              <div className="text-center">
                <FontAwesomeIcon icon={ICON_NAMES.CHART_LINE} className={`text-4xl ${themeClass.textTertiary} mb-4`} />
                <div className={`text-lg font-medium ${themeClass.textPrimary} mb-2`}>
                  TradingView Chart
                </div>
                <div className={`text-sm ${themeClass.textSecondary}`}>
                  Real-time market data visualization
                </div>
                <div className={`text-xs ${themeClass.textTertiary} mt-2`}>
                  Widget integration coming soon
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className={`${themeClass.bgCard} rounded-2xl p-6 shadow-lg border ${themeClass.borderPrimary}`}>
          <div className="flex items-center justify-between mb-6">
            <h3 className={`text-xl font-bold ${themeClass.textPrimary}`}>Recent Activity</h3>
            <button
              onClick={() => onNavigate('transactions')}
              className={`text-sm ${themeClass.textSecondary} hover:text-purple-600 transition-colors`}
            >
              View All
            </button>
          </div>
          <div className="space-y-4">
            {[
              { type: 'trade', description: 'DCA Master Pro bought BTC', amount: '+$125.50', time: '2 min ago', icon: ICON_NAMES.ARROW_UP, color: 'text-green-600' },
              { type: 'trade', description: 'Scalping Beast sold DOGE', amount: '+$89.20', time: '15 min ago', icon: ICON_NAMES.ARROW_DOWN, color: 'text-blue-600' },
              { type: 'deposit', description: 'USDT deposit confirmed', amount: '+$1,000.00', time: '1 hour ago', icon: ICON_NAMES.PLUS, color: 'text-green-600' },
              { type: 'bot', description: 'New bot "Trend Follower" activated', amount: '', time: '2 hours ago', icon: ICON_NAMES.ROBOT, color: 'text-purple-600' },
            ].map((activity, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 ${themeClass.bgSecondary} rounded-lg flex items-center justify-center`}>
                    <FontAwesomeIcon icon={activity.icon} className={`text-sm ${activity.color}`} />
                  </div>
                  <div>
                    <div className={`font-medium ${themeClass.textPrimary}`}>{activity.description}</div>
                    <div className={`text-sm ${themeClass.textTertiary}`}>{activity.time}</div>
                  </div>
                </div>
                {activity.amount && (
                  <div className={`font-bold ${activity.color}`}>
                    {activity.amount}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
