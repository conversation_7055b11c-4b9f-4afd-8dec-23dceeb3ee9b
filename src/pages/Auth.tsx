/**
 * ==============================================
 * UNIFIED AUTH COMPONENT
 * ==============================================
 * 
 * This component provides both login and signup functionality with
 * smooth animations and transitions between the two modes.
 * Features beautiful UX with creative animations and accessibility.
 */

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';
import { login, signup } from '../lib/auth';
import { useNavigate, useLocation } from 'react-router-dom';

interface AuthProps {
  onAuthSuccess?: () => void;
}

type AuthMode = 'login' | 'signup';

export const Auth: React.FC<AuthProps> = ({ onAuthSuccess }) => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Determine initial mode based on route
  const [mode, setMode] = useState<AuthMode>(
    location.pathname === '/signup' ? 'signup' : 'login'
  );
  
  const [formData, setFormData] = useState({
    name: '',
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    referralCode: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [saveUsername, setSaveUsername] = useState(false);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (mode === 'signup') {
      if (!formData.name.trim()) {
        newErrors.name = 'Full name is required';
      }

      if (!formData.username.trim()) {
        newErrors.username = 'Username is required';
      } else if (formData.username.length < 3) {
        newErrors.username = 'Username must be at least 3 characters';
      }

      if (!formData.email.trim()) {
        newErrors.email = 'Email is required';
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
        newErrors.email = 'Please enter a valid email address';
      }

      if (!formData.confirmPassword) {
        newErrors.confirmPassword = 'Please confirm your password';
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }

      if (!acceptTerms) {
        newErrors.terms = 'You must accept the terms and conditions';
      }
    } else {
      // Login validation
      if (!formData.username.trim()) {
        newErrors.username = 'Username or email is required';
      }
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (mode === 'signup' && formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      if (mode === 'login') {
        const { session, error } = await login(formData.username, formData.password);

        if (error || !session) {
          setErrors({ submit: error?.message || 'Invalid username or password' });
          setIsLoading(false);
          return;
        }

        localStorage.setItem('token', session.token);

        if (onAuthSuccess) {
          onAuthSuccess();
        }

        // Navigate to dashboard
        navigate('/dashboard');
      } else {
        // Use real Supabase signup
        const { user, error } = await signup(
          formData.email,
          formData.password,
          formData.name,
          formData.username,
          formData.referralCode || undefined
        );

        if (error) {
          setErrors({ submit: error.message });
          setIsLoading(false);
          return;
        }

        console.log('Signup successful:', user);

        // Switch to login mode after successful signup
        setMode('login');
        setFormData(prev => ({
          ...prev,
          confirmPassword: '',
          name: '',
          referralCode: '',
          password: '' // Clear password for security
        }));
        setErrors({ success: 'Account created successfully! Please check your email to verify your account, then log in.' });
      }
    } catch (error) {
      setErrors({ submit: `${mode === 'login' ? 'Login' : 'Signup'} failed. Please try again.` });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const switchMode = () => {
    const newMode = mode === 'login' ? 'signup' : 'login';
    setMode(newMode);
    setErrors({});
    setFormData({
      name: '',
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      referralCode: '',
    });
    
    // Update URL without page reload
    navigate(newMode === 'login' ? '/login' : '/signup', { replace: true });
  };

  const connectWallet = async () => {
    try {
      alert('Wallet connection will be implemented with Web3 integration');
    } catch (error) {
      setErrors({ wallet: 'Failed to connect wallet. Please try again.' });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-pink-900 to-purple-900 flex items-center justify-center p-4">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-48 h-48 bg-pink-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 w-24 h-24 bg-blue-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative w-full max-w-sm">
        {/* Enhanced Logo */}
        <div className="text-center mb-6">
          <div className="inline-flex items-center justify-center mb-4">
            <FontAwesomeIcon icon={ICON_NAMES.ROCKET} className="text-purple-400 text-4xl animate-bounce" />
          </div>
          <h1 className="text-2xl font-black text-white mb-2 transition-all duration-500">
            {mode === 'login' ? 'Welcome Back' : 'Join MemeBot Pro'}
          </h1>
          <p className="text-pink-100 text-sm font-medium drop-shadow-lg transition-all duration-500">
            {mode === 'login'
              ? 'Secure access to your crypto trading account'
              : 'Start your crypto trading journey'
            }
          </p>
        </div>

        {/* Mode Switch Buttons */}
        <div className="flex mb-6 bg-black/20 rounded-xl p-1.5 backdrop-blur-sm">
          <button
            onClick={() => mode !== 'login' && switchMode()}
            className={`flex-1 py-2 px-4 rounded-lg font-semibold text-sm transition-all duration-300 ${
              mode === 'login'
                ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg'
                : 'text-pink-200 hover:text-white hover:bg-white/10'
            }`}
          >
            <FontAwesomeIcon icon={ICON_NAMES.USER} className="mr-1.5 text-xs" />
            Sign In
          </button>
          <button
            onClick={() => mode !== 'signup' && switchMode()}
            className={`flex-1 py-2 px-4 rounded-lg font-semibold text-sm transition-all duration-300 ${
              mode === 'signup'
                ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg'
                : 'text-pink-200 hover:text-white hover:bg-white/10'
            }`}
          >
            <FontAwesomeIcon icon={ICON_NAMES.ROCKET} className="mr-1.5 text-xs" />
            Sign Up
          </button>
        </div>

        {/* Auth Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="glass-effect rounded-2xl p-6 backdrop-blur-xl border border-pink-400/30 shadow-2xl transition-all duration-500">
            
            {/* Success/Error Display */}
            {errors.success && (
              <div className="mb-4 p-3 bg-green-500/20 border border-green-400/30 rounded-lg text-green-200 text-xs animate-pulse">
                {errors.success}
              </div>
            )}

            {errors.submit && (
              <div className="mb-4 p-3 bg-red-500/20 border border-red-400/30 rounded-lg text-red-200 text-xs">
                {errors.submit}
              </div>
            )}

            <div className="space-y-4">
              {/* Full Name Field - Only for Signup */}
              <div className={`transition-all duration-500 overflow-hidden ${
                mode === 'signup' ? 'max-h-20 opacity-100' : 'max-h-0 opacity-0'
              }`}>
                {mode === 'signup' && (
                  <div className="relative">
                    <FontAwesomeIcon icon={ICON_NAMES.USER} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-emerald-400 text-lg drop-shadow-lg" />
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder="Full Name"
                      className={`w-full bg-gradient-to-r from-pink-900/60 to-pink-800/60 border ${
                        errors.name ? 'border-red-400/60' : 'border-pink-400/40'
                      } rounded-xl px-10 py-3 text-white placeholder-emerald-300 focus:bg-pink-800/70 focus:border-emerald-400/60 focus:outline-none transition-all-smooth font-medium backdrop-blur-sm text-sm autofill:bg-pink-800/80 autofill:text-white autofill:shadow-[inset_0_0_0px_1000px_rgb(157_23_77_/_0.8)]`}
                      aria-label="Full Name"
                    />
                    {errors.name && (
                      <p className="mt-1 text-red-300 text-xs" role="alert">{errors.name}</p>
                    )}
                  </div>
                )}
              </div>

              {/* Username/Email Field */}
              <div>
                <div className="relative">
                  <FontAwesomeIcon icon={mode === 'signup' ? ICON_NAMES.USER : ICON_NAMES.EMAIL} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-400 text-lg drop-shadow-lg" />
                  <input
                    type="text"
                    value={formData.username}
                    onChange={(e) => handleInputChange('username', e.target.value)}
                    placeholder={mode === 'signup' ? 'Username' : 'Email Address'}
                    className={`w-full bg-gradient-to-r from-pink-900/60 to-pink-800/60 border ${
                      errors.username ? 'border-red-400/60' : 'border-pink-400/40'
                    } rounded-xl px-10 py-3 text-white placeholder-blue-300 focus:bg-pink-800/70 focus:border-blue-400/60 focus:outline-none transition-all-smooth font-medium backdrop-blur-sm text-sm autofill:bg-pink-800/80 autofill:text-white autofill:shadow-[inset_0_0_0px_1000px_rgb(157_23_77_/_0.8)]`}
                    required
                    aria-label={mode === 'signup' ? 'Username' : 'Email Address'}
                  />
                </div>
                {errors.username && (
                  <p className="mt-1 text-red-300 text-xs" role="alert">{errors.username}</p>
                )}
              </div>

              {/* Email Field - Only for Signup */}
              <div className={`transition-all duration-500 overflow-hidden ${
                mode === 'signup' ? 'max-h-20 opacity-100' : 'max-h-0 opacity-0'
              }`}>
                {mode === 'signup' && (
                  <div className="relative">
                    <FontAwesomeIcon icon={ICON_NAMES.EMAIL} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-cyan-400 text-lg drop-shadow-lg" />
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="Email Address"
                      className={`w-full bg-gradient-to-r from-pink-900/60 to-pink-800/60 border ${
                        errors.email ? 'border-red-400/60' : 'border-pink-400/40'
                      } rounded-xl px-10 py-3 text-white placeholder-cyan-300 focus:bg-pink-800/70 focus:border-cyan-400/60 focus:outline-none transition-all-smooth font-medium backdrop-blur-sm text-sm autofill:bg-pink-800/80 autofill:text-white autofill:shadow-[inset_0_0_0px_1000px_rgb(157_23_77_/_0.8)]`}
                      aria-label="Email Address"
                    />
                    {errors.email && (
                      <p className="mt-1 text-red-300 text-xs" role="alert">{errors.email}</p>
                    )}
                  </div>
                )}
              </div>

              {/* Password Field */}
              <div>
                <div className="relative">
                  <FontAwesomeIcon icon={ICON_NAMES.LOCK} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-yellow-400 text-lg drop-shadow-lg" />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    placeholder="Password"
                    className={`w-full bg-gradient-to-r from-pink-900/60 to-pink-800/60 border ${
                      errors.password ? 'border-red-400/60' : 'border-pink-400/40'
                    } rounded-xl px-10 py-3 pr-12 text-white placeholder-yellow-300 focus:bg-pink-800/70 focus:border-yellow-400/60 focus:outline-none transition-all-smooth font-medium backdrop-blur-sm text-sm autofill:bg-pink-800/80 autofill:text-white autofill:shadow-[inset_0_0_0px_1000px_rgb(157_23_77_/_0.8)]`}
                    required
                    aria-label="Password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-yellow-300 hover:text-yellow-100 transition-colors p-1 rounded-lg hover:bg-pink-700/50"
                    aria-label={showPassword ? 'Hide password' : 'Show password'}
                  >
                    <FontAwesomeIcon icon={showPassword ? ICON_NAMES.EYE_SLASH : ICON_NAMES.EYE} className="text-sm drop-shadow-lg" />
                  </button>
                </div>
                {errors.password && (
                  <p className="mt-1 text-red-300 text-xs" role="alert">{errors.password}</p>
                )}
              </div>

              {/* Confirm Password Field - Only for Signup */}
              <div className={`transition-all duration-500 overflow-hidden ${
                mode === 'signup' ? 'max-h-20 opacity-100' : 'max-h-0 opacity-0'
              }`}>
                {mode === 'signup' && (
                  <div className="relative">
                    <FontAwesomeIcon icon={ICON_NAMES.SHIELD} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-green-400 text-lg drop-shadow-lg" />
                    <input
                      type={showConfirmPassword ? 'text' : 'password'}
                      value={formData.confirmPassword}
                      onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                      placeholder="Confirm Password"
                      className={`w-full bg-gradient-to-r from-pink-900/60 to-pink-800/60 border ${
                        errors.confirmPassword ? 'border-red-400/60' : 'border-pink-400/40'
                      } rounded-xl px-10 py-3 pr-12 text-white placeholder-green-300 focus:bg-pink-800/70 focus:border-green-400/60 focus:outline-none transition-all-smooth font-medium backdrop-blur-sm text-sm autofill:bg-pink-800/80 autofill:text-white autofill:shadow-[inset_0_0_0px_1000px_rgb(157_23_77_/_0.8)]`}
                      aria-label="Confirm Password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-300 hover:text-green-100 transition-colors p-1 rounded-lg hover:bg-pink-700/50"
                      aria-label={showConfirmPassword ? 'Hide confirm password' : 'Show confirm password'}
                    >
                      <FontAwesomeIcon icon={showConfirmPassword ? ICON_NAMES.EYE_SLASH : ICON_NAMES.EYE} className="text-sm drop-shadow-lg" />
                    </button>
                    {errors.confirmPassword && (
                      <p className="mt-1 text-red-300 text-xs" role="alert">{errors.confirmPassword}</p>
                    )}
                  </div>
                )}
              </div>

              {/* Referral Code Field - Only for Signup */}
              <div className={`transition-all duration-500 overflow-hidden ${
                mode === 'signup' ? 'max-h-16 opacity-100' : 'max-h-0 opacity-0'
              }`}>
                {mode === 'signup' && (
                  <div className="relative">
                    <FontAwesomeIcon icon={ICON_NAMES.KEY} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-purple-400 text-lg drop-shadow-lg" />
                    <input
                      type="text"
                      value={formData.referralCode}
                      onChange={(e) => handleInputChange('referralCode', e.target.value)}
                      placeholder="Referral Code (Optional)"
                      className="w-full bg-gradient-to-r from-pink-900/60 to-pink-800/60 border border-pink-400/40 rounded-xl px-10 py-3 text-white placeholder-purple-300 focus:bg-pink-800/70 focus:border-purple-400/60 focus:outline-none transition-all-smooth font-medium backdrop-blur-sm text-sm autofill:bg-pink-800/80 autofill:text-white autofill:shadow-[inset_0_0_0px_1000px_rgb(157_23_77_/_0.8)]"
                      aria-label="Referral Code"
                    />
                  </div>
                )}
              </div>

              {/* Save Username - Only for Login */}
              <div className={`transition-all duration-500 overflow-hidden ${
                mode === 'login' ? 'max-h-12 opacity-100' : 'max-h-0 opacity-0'
              }`}>
                {mode === 'login' && (
                  <div className="flex items-center space-x-2">
                    <button
                      type="button"
                      onClick={() => setSaveUsername(!saveUsername)}
                      className={`w-4 h-4 rounded border border-pink-300/60 flex items-center justify-center transition-all-smooth ${
                        saveUsername ? 'bg-gradient-to-r from-purple-400 to-pink-400 border-purple-400' : 'hover:border-pink-200'
                      }`}
                    >
                      {saveUsername && <div className="w-2 h-2 bg-white rounded-sm"></div>}
                    </button>
                    <span className="text-pink-100 text-xs font-medium">Remember me</span>
                  </div>
                )}
              </div>

              {/* Terms and Conditions - Only for Signup */}
              <div className={`transition-all duration-500 overflow-hidden ${
                mode === 'signup' ? 'max-h-16 opacity-100' : 'max-h-0 opacity-0'
              }`}>
                {mode === 'signup' && (
                  <div>
                    <label className="flex items-start space-x-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={acceptTerms}
                        onChange={(e) => {
                          setAcceptTerms(e.target.checked);
                          if (errors.terms) {
                            setErrors(prev => ({ ...prev, terms: '' }));
                          }
                        }}
                        className="mt-0.5 w-3 h-3 text-purple-600 bg-transparent border border-pink-400/40 rounded focus:ring-purple-500 focus:ring-1"
                      />
                      <span className="text-pink-200 text-xs leading-relaxed">
                        I agree to the{' '}
                        <a href="#" className="text-purple-300 hover:text-purple-200 underline">
                          Terms
                        </a>{' '}
                        and{' '}
                        <a href="#" className="text-purple-300 hover:text-purple-200 underline">
                          Privacy Policy
                        </a>
                      </span>
                    </label>
                    {errors.terms && (
                      <p className="mt-1 text-red-300 text-xs" role="alert">{errors.terms}</p>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full mt-6 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:from-gray-600 disabled:to-gray-600 text-white font-semibold py-3 px-6 rounded-xl transition-all-smooth transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed shadow-xl text-sm"
            >
              {isLoading ? (
                <div className="flex items-center justify-center space-x-2">
                  <FontAwesomeIcon icon={ICON_NAMES.LOADING} className="animate-spin text-xs" />
                  <span>{mode === 'login' ? 'Signing In...' : 'Creating Account...'}</span>
                </div>
              ) : (
                <div className="flex items-center justify-center space-x-2">
                  <FontAwesomeIcon icon={mode === 'login' ? ICON_NAMES.USER : ICON_NAMES.ROCKET} className="text-xs" />
                  <span>{mode === 'login' ? 'Sign In' : 'Create Account'}</span>
                </div>
              )}
            </button>
          </div>
        </form>

        {/* Alternative Options */}
        <div className="mt-6 text-center">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-pink-400/30"></div>
            </div>
            <div className="relative flex justify-center text-xs">
              <span className="px-3 bg-transparent text-pink-200 font-medium">Or continue with</span>
            </div>
          </div>

          <button
            onClick={connectWallet}
            className="mt-4 w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-xl transition-all-smooth transform hover:scale-105 shadow-xl text-sm"
          >
            <div className="flex items-center justify-center space-x-2">
              <FontAwesomeIcon icon={ICON_NAMES.WALLET} className="text-xs" />
              <span>Connect Crypto Wallet</span>
            </div>
          </button>
        </div>

        {/* Footer */}
        <div className="text-center mt-6 space-y-1">
          <p className="text-pink-200 text-xs font-medium drop-shadow-lg">© 2024 MemeBot Pro</p>
          <p className="text-pink-300 text-xs drop-shadow-lg opacity-75">
            Automated crypto trading platform
          </p>
        </div>
      </div>
    </div>
  );
};
