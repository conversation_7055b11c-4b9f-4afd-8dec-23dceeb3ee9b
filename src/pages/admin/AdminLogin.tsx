/**
 * ==============================================
 * ADMIN LOGIN PAGE
 * ==============================================
 */

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../../lib/icons';
import { adminSignIn } from '../../lib/adminSupabase';

interface AdminLoginProps {
  onLoginSuccess: () => void;
}

export function AdminLogin({ onLoginSuccess }: AdminLoginProps) {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const { data, error } = await adminSignIn(formData.email, formData.password);

      if (error) {
        setError(error);
      } else if (data) {
        // Call the success callback to refresh admin session
        onLoginSuccess();
      }
    } catch (err) {
      setError('Authentication failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError('');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-gray-900 flex items-center justify-center p-4">
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-48 h-48 bg-purple-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>

      <div className="relative w-full max-w-md">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center mb-6 p-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full">
            <FontAwesomeIcon icon={ICON_NAMES.SHIELD} className="h-12 w-12 text-white" />
          </div>
          <h1 className="text-3xl font-black text-white mb-2">Admin Portal</h1>
          <p className="text-blue-100 text-lg font-medium drop-shadow-lg">
            Secure access to MemeBot Pro administration
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-8 border border-blue-400/30 shadow-2xl">
            
            {error && (
              <div className="mb-6 p-4 bg-red-500/20 border border-red-400/30 rounded-xl text-red-200 text-sm">
                {error}
              </div>
            )}

            <div className="space-y-5">
              <div>
                <div className="relative">
                  <FontAwesomeIcon icon={ICON_NAMES.EMAIL} className="absolute left-4 top-1/2 transform -translate-y-1/2 text-blue-400 text-lg drop-shadow-lg" />
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="Admin Email"
                    className="w-full bg-gradient-to-r from-blue-900/60 to-purple-800/60 border border-blue-400/40 rounded-xl px-12 py-4 text-white placeholder-blue-200 focus:bg-blue-800/70 focus:border-blue-300/60 focus:outline-none transition-all duration-300 font-medium backdrop-blur-sm"
                    required
                  />
                </div>
              </div>

              <div>
                <div className="relative">
                  <FontAwesomeIcon icon={ICON_NAMES.LOCK} className="absolute left-4 top-1/2 transform -translate-y-1/2 text-purple-400 text-lg drop-shadow-lg" />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    placeholder="Password"
                    className="w-full bg-gradient-to-r from-blue-900/60 to-purple-800/60 border border-blue-400/40 rounded-xl px-12 py-4 pr-16 text-white placeholder-purple-200 focus:bg-blue-800/70 focus:border-purple-300/60 focus:outline-none transition-all duration-300 font-medium backdrop-blur-sm"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 text-purple-300 hover:text-purple-100 transition-colors p-1 rounded-lg hover:bg-purple-700/50"
                  >
                    <FontAwesomeIcon icon={showPassword ? ICON_NAMES.EYE_SLASH : ICON_NAMES.EYE} />
                  </button>
                </div>
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full mt-8 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-600 disabled:to-gray-600 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed shadow-2xl"
            >
              {isLoading ? (
                <div className="flex items-center justify-center space-x-2">
                  <FontAwesomeIcon icon={ICON_NAMES.LOADING} className="animate-spin" />
                  <span>Authenticating...</span>
                </div>
              ) : (
                <div className="flex items-center justify-center space-x-2">
                  <FontAwesomeIcon icon={ICON_NAMES.SHIELD} />
                  <span>Access Admin Portal</span>
                </div>
              )}
            </button>
          </div>
        </form>

        <div className="text-center mt-8 space-y-2">
          <p className="text-blue-200 text-sm font-medium drop-shadow-lg">© 2024 MemeBot Pro</p>
          <p className="text-blue-300 text-xs drop-shadow-lg opacity-75">
            Secure Administrative Interface
          </p>
        </div>
      </div>
    </div>
  );
}
