/**
 * ==============================================
 * ADMIN TRANSACTION MANAGEMENT
 * ==============================================
 */

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../../lib/icons';
import { getTransactions, approveTransaction } from '../../lib/adminSupabase';

interface Transaction {
  id: string;
  user_id: string;
  transaction_type: string;
  amount: number;
  currency: string;
  status: string;
  description: string;
  created_at: string;
  users: {
    name: string;
    email: string;
    username: string;
  };
}

export function TransactionManagement() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalTransactions, setTotalTransactions] = useState(0);
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterType, setFilterType] = useState('all');

  const transactionsPerPage = 50;

  useEffect(() => {
    loadTransactions();
  }, [currentPage]);

  const loadTransactions = async () => {
    try {
      setLoading(true);
      const { data, error, count } = await getTransactions(currentPage, transactionsPerPage);
      
      if (error) {
        setError('Failed to load transactions');
        console.error('Transactions error:', error);
      } else {
        setTransactions(data || []);
        setTotalTransactions(count || 0);
      }
    } catch (err) {
      setError('Failed to load transactions');
      console.error('Transactions error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleApproveTransaction = async (transactionId: string) => {
    try {
      const { data, error } = await approveTransaction(transactionId);
      
      if (error) {
        alert('Failed to approve transaction');
      } else {
        setTransactions(transactions.map(tx => 
          tx.id === transactionId ? { ...tx, status: 'completed' } : tx
        ));
      }
    } catch (err) {
      alert('Failed to approve transaction');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-400 bg-green-400/20';
      case 'pending': return 'text-yellow-400 bg-yellow-400/20';
      case 'failed': return 'text-red-400 bg-red-400/20';
      default: return 'text-gray-400 bg-gray-400/20';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'deposit': return 'text-green-400';
      case 'withdrawal': return 'text-red-400';
      case 'profit': return 'text-blue-400';
      case 'loss': return 'text-orange-400';
      default: return 'text-gray-400';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'deposit': return ICON_NAMES.ARROW_DOWN;
      case 'withdrawal': return ICON_NAMES.ARROW_UP;
      case 'profit': return ICON_NAMES.TRENDING_UP;
      case 'loss': return ICON_NAMES.TRENDING_DOWN;
      default: return ICON_NAMES.WALLET;
    }
  };

  const filteredTransactions = transactions.filter(tx => {
    const statusMatch = filterStatus === 'all' || tx.status === filterStatus;
    const typeMatch = filterType === 'all' || tx.transaction_type === filterType;
    return statusMatch && typeMatch;
  });

  const totalPages = Math.ceil(totalTransactions / transactionsPerPage);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Transaction Management</h1>
          <p className="text-gray-400 mt-1">Monitor and manage all platform transactions</p>
        </div>
        <div className="text-sm text-gray-400">
          Total Transactions: {totalTransactions}
        </div>
      </div>

      {/* Filters */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-300 mb-2">Filter by Status</label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
            >
              <option value="all">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="completed">Completed</option>
              <option value="failed">Failed</option>
            </select>
          </div>
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-300 mb-2">Filter by Type</label>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
            >
              <option value="all">All Types</option>
              <option value="deposit">Deposits</option>
              <option value="withdrawal">Withdrawals</option>
              <option value="profit">Profits</option>
              <option value="loss">Losses</option>
            </select>
          </div>
          <div className="flex items-end">
            <button
              onClick={loadTransactions}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              <FontAwesomeIcon icon={ICON_NAMES.FILTER} className="mr-2" />
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Transactions Table */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <FontAwesomeIcon icon={ICON_NAMES.LOADING} className="h-8 w-8 text-blue-500 animate-spin" />
          </div>
        ) : error ? (
          <div className="p-6 text-center">
            <p className="text-red-400">{error}</p>
            <button 
              onClick={loadTransactions}
              className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
            >
              Retry
            </button>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Description
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-700">
                  {filteredTransactions.map((transaction) => (
                    <tr key={transaction.id} className="hover:bg-gray-700/50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-white">{transaction.users?.name}</div>
                          <div className="text-sm text-gray-400">{transaction.users?.email}</div>
                          <div className="text-xs text-gray-500">@{transaction.users?.username}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2">
                          <FontAwesomeIcon 
                            icon={getTypeIcon(transaction.transaction_type)} 
                            className={`h-4 w-4 ${getTypeColor(transaction.transaction_type)}`}
                          />
                          <span className={`text-sm font-medium ${getTypeColor(transaction.transaction_type)}`}>
                            {transaction.transaction_type.toUpperCase()}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-white">
                          {transaction.transaction_type === 'withdrawal' || transaction.transaction_type === 'loss' ? '-' : '+'}
                          ${transaction.amount.toLocaleString()} {transaction.currency}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(transaction.status)}`}>
                          {transaction.status.toUpperCase()}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-300 max-w-xs truncate">
                          {transaction.description}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                        {new Date(transaction.created_at).toLocaleDateString()} {new Date(transaction.created_at).toLocaleTimeString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          {transaction.status === 'pending' && (
                            <button
                              onClick={() => handleApproveTransaction(transaction.id)}
                              className="text-green-400 hover:text-green-300"
                              title="Approve Transaction"
                            >
                              <FontAwesomeIcon icon={ICON_NAMES.CHECK} />
                            </button>
                          )}
                          <button className="text-blue-400 hover:text-blue-300" title="View Details">
                            <FontAwesomeIcon icon={ICON_NAMES.EYE} />
                          </button>
                          <button className="text-yellow-400 hover:text-yellow-300" title="Edit">
                            <FontAwesomeIcon icon={ICON_NAMES.EDIT} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="bg-gray-700 px-6 py-3 flex items-center justify-between">
              <div className="text-sm text-gray-400">
                Showing {((currentPage - 1) * transactionsPerPage) + 1} to {Math.min(currentPage * transactionsPerPage, totalTransactions)} of {totalTransactions} transactions
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 bg-gray-600 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-500"
                >
                  Previous
                </button>
                <span className="px-3 py-1 text-gray-400">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 bg-gray-600 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-500"
                >
                  Next
                </button>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Transaction Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm font-medium">Total Deposits</p>
              <p className="text-2xl font-bold text-green-400">
                ${filteredTransactions
                  .filter(tx => tx.transaction_type === 'deposit' && tx.status === 'completed')
                  .reduce((sum, tx) => sum + tx.amount, 0)
                  .toLocaleString()}
              </p>
            </div>
            <FontAwesomeIcon icon={ICON_NAMES.ARROW_DOWN} className="h-8 w-8 text-green-400" />
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm font-medium">Total Withdrawals</p>
              <p className="text-2xl font-bold text-red-400">
                ${filteredTransactions
                  .filter(tx => tx.transaction_type === 'withdrawal' && tx.status === 'completed')
                  .reduce((sum, tx) => sum + tx.amount, 0)
                  .toLocaleString()}
              </p>
            </div>
            <FontAwesomeIcon icon={ICON_NAMES.ARROW_UP} className="h-8 w-8 text-red-400" />
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm font-medium">Pending Transactions</p>
              <p className="text-2xl font-bold text-yellow-400">
                {filteredTransactions.filter(tx => tx.status === 'pending').length}
              </p>
            </div>
            <FontAwesomeIcon icon={ICON_NAMES.WARNING} className="h-8 w-8 text-yellow-400" />
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm font-medium">Net Profit</p>
              <p className="text-2xl font-bold text-blue-400">
                ${filteredTransactions
                  .filter(tx => tx.transaction_type === 'profit' && tx.status === 'completed')
                  .reduce((sum, tx) => sum + tx.amount, 0)
                  .toLocaleString()}
              </p>
            </div>
            <FontAwesomeIcon icon={ICON_NAMES.CHART} className="h-8 w-8 text-blue-400" />
          </div>
        </div>
      </div>
    </div>
  );
}
