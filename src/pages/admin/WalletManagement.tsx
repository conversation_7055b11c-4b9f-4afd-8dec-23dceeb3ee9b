/**
 * ==============================================
 * ADMIN WALLET MANAGEMENT
 * ==============================================
 * 
 * This component allows admins to:
 * - Create wallet bundles for different cryptocurrencies
 * - Generate and manage crypto wallet addresses
 * - Assign wallet bundles to users
 * - Monitor deposit transactions
 */

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../../lib/icons';

interface Cryptocurrency {
  id: string;
  symbol: string;
  name: string;
  network: string;
  is_active: boolean;
  min_deposit: number;
}

interface WalletBundle {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
  wallet_count: number;
  assigned_users: number;
  created_at: string;
}

interface CryptoWallet {
  id: string;
  bundle_id: string;
  cryptocurrency: Cryptocurrency;
  address: string;
  is_active: boolean;
  created_at: string;
}

export const WalletManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'bundles' | 'wallets' | 'cryptocurrencies'>('bundles');
  const [walletBundles, setWalletBundles] = useState<WalletBundle[]>([]);
  const [cryptoWallets, setCryptoWallets] = useState<CryptoWallet[]>([]);
  const [cryptocurrencies, setCryptocurrencies] = useState<Cryptocurrency[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateBundle, setShowCreateBundle] = useState(false);
  const [showAddWallet, setShowAddWallet] = useState(false);
  const [selectedBundle, setSelectedBundle] = useState<string>('');

  // Form states
  const [bundleName, setBundleName] = useState('');
  const [bundleDescription, setBundleDescription] = useState('');
  const [selectedCrypto, setSelectedCrypto] = useState('');
  const [walletAddress, setWalletAddress] = useState('');

  useEffect(() => {
    loadWalletData();
  }, []);

  const loadWalletData = async () => {
    try {
      // Mock data for development - replace with real API calls
      const mockCryptocurrencies: Cryptocurrency[] = [
        {
          id: '1',
          symbol: 'BTC',
          name: 'Bitcoin',
          network: 'Bitcoin',
          is_active: true,
          min_deposit: 0.0001
        },
        {
          id: '2',
          symbol: 'ETH',
          name: 'Ethereum',
          network: 'Ethereum',
          is_active: true,
          min_deposit: 0.001
        },
        {
          id: '3',
          symbol: 'USDT',
          name: 'Tether USD',
          network: 'Ethereum',
          is_active: true,
          min_deposit: 1.0
        },
        {
          id: '4',
          symbol: 'USDC',
          name: 'USD Coin',
          network: 'Ethereum',
          is_active: true,
          min_deposit: 1.0
        }
      ];

      const mockBundles: WalletBundle[] = [
        {
          id: '1',
          name: 'Standard Multi-Wallet',
          description: 'Default wallet bundle with BTC, ETH, USDT, USDC support',
          is_active: true,
          wallet_count: 4,
          assigned_users: 25,
          created_at: '2024-01-01T00:00:00Z'
        },
        {
          id: '2',
          name: 'Premium Multi-Wallet',
          description: 'Premium wallet bundle with additional altcoin support',
          is_active: true,
          wallet_count: 8,
          assigned_users: 12,
          created_at: '2024-01-02T00:00:00Z'
        }
      ];

      const mockWallets: CryptoWallet[] = [
        {
          id: '1',
          bundle_id: '1',
          cryptocurrency: mockCryptocurrencies[0],
          address: '**********************************',
          is_active: true,
          created_at: '2024-01-01T00:00:00Z'
        },
        {
          id: '2',
          bundle_id: '1',
          cryptocurrency: mockCryptocurrencies[1],
          address: '0x742d35Cc6634C0532925a3b8D4C9db96590b5',
          is_active: true,
          created_at: '2024-01-01T00:00:00Z'
        }
      ];

      setCryptocurrencies(mockCryptocurrencies);
      setWalletBundles(mockBundles);
      setCryptoWallets(mockWallets);
      setLoading(false);
    } catch (error) {
      console.error('Error loading wallet data:', error);
      setLoading(false);
    }
  };

  const handleCreateBundle = async () => {
    if (!bundleName.trim()) return;

    const newBundle: WalletBundle = {
      id: `bundle_${Date.now()}`,
      name: bundleName,
      description: bundleDescription,
      is_active: true,
      wallet_count: 0,
      assigned_users: 0,
      created_at: new Date().toISOString()
    };

    setWalletBundles([...walletBundles, newBundle]);
    setBundleName('');
    setBundleDescription('');
    setShowCreateBundle(false);
  };

  const handleAddWallet = async () => {
    if (!selectedBundle || !selectedCrypto || !walletAddress.trim()) return;

    const crypto = cryptocurrencies.find(c => c.id === selectedCrypto);
    if (!crypto) return;

    const newWallet: CryptoWallet = {
      id: `wallet_${Date.now()}`,
      bundle_id: selectedBundle,
      cryptocurrency: crypto,
      address: walletAddress,
      is_active: true,
      created_at: new Date().toISOString()
    };

    setCryptoWallets([...cryptoWallets, newWallet]);
    setWalletAddress('');
    setSelectedCrypto('');
    setSelectedBundle('');
    setShowAddWallet(false);

    // Update bundle wallet count
    setWalletBundles(bundles => 
      bundles.map(bundle => 
        bundle.id === selectedBundle 
          ? { ...bundle, wallet_count: bundle.wallet_count + 1 }
          : bundle
      )
    );
  };

  const generateRandomAddress = (cryptoSymbol: string) => {
    // Mock address generation - in real implementation, use proper crypto libraries
    const addresses = {
      'BTC': '1' + Math.random().toString(36).substring(2, 35),
      'ETH': '0x' + Math.random().toString(16).substring(2, 42),
      'USDT': '0x' + Math.random().toString(16).substring(2, 42),
      'USDC': '0x' + Math.random().toString(16).substring(2, 42)
    };
    return addresses[cryptoSymbol as keyof typeof addresses] || 'Generated Address';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <FontAwesomeIcon icon={ICON_NAMES.LOADING} className="text-4xl text-blue-500 animate-spin mb-4" />
          <p className="text-gray-300">Loading wallet management...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-black text-white mb-2">Wallet Management</h1>
          <p className="text-gray-400">Manage multi-wallet bundles and cryptocurrency addresses</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowCreateBundle(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            <FontAwesomeIcon icon={ICON_NAMES.PLUS} className="mr-2" />
            Create Bundle
          </button>
          <button
            onClick={() => setShowAddWallet(true)}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            <FontAwesomeIcon icon={ICON_NAMES.WALLET} className="mr-2" />
            Add Wallet
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-800 p-1 rounded-lg">
        {[
          { id: 'bundles', label: 'Wallet Bundles', icon: ICON_NAMES.FOLDER },
          { id: 'wallets', label: 'Individual Wallets', icon: ICON_NAMES.WALLET },
          { id: 'cryptocurrencies', label: 'Supported Coins', icon: ICON_NAMES.COINS }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md font-medium transition-colors ${
              activeTab === tab.id
                ? 'bg-gray-700 text-white'
                : 'text-gray-400 hover:text-white hover:bg-gray-700'
            }`}
          >
            <FontAwesomeIcon icon={tab.icon} />
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'bundles' && (
        <div className="grid gap-6">
          {walletBundles.map((bundle) => (
            <div key={bundle.id} className="bg-gray-800 rounded-xl p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-xl font-bold text-white mb-2">{bundle.name}</h3>
                  <p className="text-gray-400 mb-3">{bundle.description}</p>
                  <div className="flex space-x-6 text-sm">
                    <div>
                      <span className="text-gray-500">Wallets:</span>
                      <span className="text-white font-medium ml-2">{bundle.wallet_count}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Assigned Users:</span>
                      <span className="text-white font-medium ml-2">{bundle.assigned_users}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Status:</span>
                      <span className={`ml-2 px-2 py-1 rounded text-xs font-medium ${
                        bundle.is_active ? 'bg-green-900 text-green-300' : 'bg-red-900 text-red-300'
                      }`}>
                        {bundle.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                    Edit
                  </button>
                  <button className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm">
                    View Wallets
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'wallets' && (
        <div className="bg-gray-800 rounded-xl overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-700">
                <tr>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Cryptocurrency</th>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Address</th>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Bundle</th>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Status</th>
                  <th className="text-left py-4 px-6 text-gray-300 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {cryptoWallets.map((wallet) => (
                  <tr key={wallet.id} className="border-t border-gray-700">
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                          <span className="text-xs font-bold text-white">
                            {wallet.cryptocurrency.symbol.substring(0, 2)}
                          </span>
                        </div>
                        <div>
                          <div className="text-white font-medium">{wallet.cryptocurrency.symbol}</div>
                          <div className="text-gray-400 text-sm">{wallet.cryptocurrency.name}</div>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div className="font-mono text-sm text-gray-300 break-all">
                        {wallet.address}
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <span className="text-gray-300">
                        {walletBundles.find(b => b.id === wallet.bundle_id)?.name || 'Unknown'}
                      </span>
                    </td>
                    <td className="py-4 px-6">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        wallet.is_active ? 'bg-green-900 text-green-300' : 'bg-red-900 text-red-300'
                      }`}>
                        {wallet.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex space-x-2">
                        <button className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                          Edit
                        </button>
                        <button className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm">
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {activeTab === 'cryptocurrencies' && (
        <div className="grid gap-4">
          {cryptocurrencies.map((crypto) => (
            <div key={crypto.id} className="bg-gray-800 rounded-xl p-6 flex justify-between items-center">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gray-600 rounded-full flex items-center justify-center">
                  <span className="text-lg font-bold text-white">{crypto.symbol.substring(0, 2)}</span>
                </div>
                <div>
                  <h3 className="text-lg font-bold text-white">{crypto.name}</h3>
                  <p className="text-gray-400">{crypto.symbol} • {crypto.network}</p>
                  <p className="text-sm text-gray-500">Min deposit: {crypto.min_deposit} {crypto.symbol}</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  crypto.is_active ? 'bg-green-900 text-green-300' : 'bg-red-900 text-red-300'
                }`}>
                  {crypto.is_active ? 'Active' : 'Inactive'}
                </span>
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                  Configure
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Create Bundle Modal */}
      {showCreateBundle && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-xl p-6 w-full max-w-md">
            <h3 className="text-xl font-bold text-white mb-4">Create Wallet Bundle</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Bundle Name</label>
                <input
                  type="text"
                  value={bundleName}
                  onChange={(e) => setBundleName(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                  placeholder="e.g., Premium Multi-Wallet"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Description</label>
                <textarea
                  value={bundleDescription}
                  onChange={(e) => setBundleDescription(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                  rows={3}
                  placeholder="Describe this wallet bundle..."
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowCreateBundle(false)}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateBundle}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
              >
                Create Bundle
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add Wallet Modal */}
      {showAddWallet && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-xl p-6 w-full max-w-md">
            <h3 className="text-xl font-bold text-white mb-4">Add Wallet to Bundle</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Wallet Bundle</label>
                <select
                  value={selectedBundle}
                  onChange={(e) => setSelectedBundle(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                >
                  <option value="">Select a bundle</option>
                  {walletBundles.map((bundle) => (
                    <option key={bundle.id} value={bundle.id}>{bundle.name}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Cryptocurrency</label>
                <select
                  value={selectedCrypto}
                  onChange={(e) => setSelectedCrypto(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                >
                  <option value="">Select cryptocurrency</option>
                  {cryptocurrencies.map((crypto) => (
                    <option key={crypto.id} value={crypto.id}>{crypto.symbol} - {crypto.name}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Wallet Address</label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={walletAddress}
                    onChange={(e) => setWalletAddress(e.target.value)}
                    className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white font-mono text-sm"
                    placeholder="Enter wallet address"
                  />
                  <button
                    onClick={() => {
                      const crypto = cryptocurrencies.find(c => c.id === selectedCrypto);
                      if (crypto) {
                        setWalletAddress(generateRandomAddress(crypto.symbol));
                      }
                    }}
                    className="px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm"
                  >
                    Generate
                  </button>
                </div>
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowAddWallet(false)}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg"
              >
                Cancel
              </button>
              <button
                onClick={handleAddWallet}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg"
              >
                Add Wallet
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
