import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';
import { Portfolio, TradingBot, BotInvestment, User } from '../types';
import { formatCurrency, formatPercentage, formatDate, getRelativeTime } from '../lib/utils';

interface DashboardProps {
  portfolio: Portfolio | null;
  tradingBots: TradingBot[];
  botInvestments: BotInvestment[];
  user: User;
  onBotClick: (botId: string) => void;
  onTabChange: (tab: string) => void;
}

export const Dashboard: React.FC<DashboardProps> = ({
  portfolio,
  tradingBots,
  botInvestments,
  user,
  onBotClick,
  onTabChange
}) => {
  const totalPortfolioValue = portfolio?.total_value || 0;
  const totalInvested = portfolio?.total_invested || 0;
  const totalProfitLoss = portfolio?.total_profit_loss || 0;
  const profitLossPercentage = portfolio?.profit_loss_percentage || 0;

  // Get greeting based on time of day
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  };

  // Get top performing bots (first 3)
  const topBots = tradingBots.slice(0, 3);

  // Get recent bot activities
  const recentActivities = botInvestments.slice(0, 3);

  return (
    <div className="p-4 md:p-6 pb-32 md:pb-6 space-y-6 md:space-y-8">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl md:rounded-3xl p-6 md:p-8 text-white shadow-glow">
        <div className="flex items-center justify-between mb-4 md:mb-6">
          <div>
            <h1 className="text-2xl md:text-3xl font-black mb-1 md:mb-2">
              {getGreeting()}, {user.name || user.username}!
            </h1>
            <p className="opacity-90 text-base md:text-lg font-medium">Here's your crypto trading overview</p>
          </div>
          <div className="w-12 h-12 md:w-16 md:h-16 bg-white/20 rounded-xl md:rounded-2xl flex items-center justify-center">
            <FontAwesomeIcon icon={ICON_NAMES.CHART} className="text-white text-xl" />
          </div>
        </div>

        <div className="bg-white/20 backdrop-blur-sm rounded-xl md:rounded-2xl p-4 md:p-6">
          <div className="flex items-center space-x-2 mb-2">
            <FontAwesomeIcon icon={ICON_NAMES.WALLET} className="text-white/80" />
            <div className="text-xs md:text-sm opacity-80 font-medium">Total Portfolio Value</div>
          </div>
          <div className="text-2xl md:text-4xl font-black">{formatCurrency(totalPortfolioValue)}</div>
          <div className={`text-xs md:text-sm opacity-80 mt-1 md:mt-2 ${totalProfitLoss >= 0 ? 'text-green-300' : 'text-red-300'}`}>
            {formatPercentage(profitLossPercentage)} ({totalProfitLoss >= 0 ? '+' : ''}{formatCurrency(totalProfitLoss)})
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-2 gap-3 md:gap-4">
        <button
          onClick={() => onTabChange('bots')}
          className="glass-effect rounded-xl md:rounded-2xl p-4 md:p-6 hover:shadow-glow transition-all-smooth transform hover:scale-105"
        >
          <div className="flex flex-col md:flex-row items-center md:justify-center space-y-2 md:space-y-0 md:space-x-3">
            <div className="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl md:rounded-2xl flex items-center justify-center">
              <FontAwesomeIcon icon={ICON_NAMES.ROBOT} className="text-white" />
            </div>
            <div className="text-center md:text-left">
              <div className="text-sm md:text-lg font-bold text-gray-900">Trading Bots</div>
              <div className="text-xs md:text-sm text-gray-600 font-medium">Explore bots</div>
            </div>
          </div>
        </button>

        <button
          onClick={() => onTabChange('wallet')}
          className="glass-effect rounded-xl md:rounded-2xl p-4 md:p-6 hover:shadow-glow transition-all-smooth transform hover:scale-105"
        >
          <div className="flex flex-col md:flex-row items-center md:justify-center space-y-2 md:space-y-0 md:space-x-3">
            <div className="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl md:rounded-2xl flex items-center justify-center">
              <FontAwesomeIcon icon={ICON_NAMES.WALLET} className="text-white" />
            </div>
            <div className="text-center md:text-left">
              <div className="text-sm md:text-lg font-bold text-gray-900">Wallet</div>
              <div className="text-xs md:text-sm text-gray-600 font-medium">Manage funds</div>
            </div>
          </div>
        </button>
      </div>

      {/* Top Performing Bots */}
      <div>
        <div className="flex items-center justify-between mb-4 md:mb-6">
          <h2 className="text-xl md:text-2xl font-black text-gray-900">Top Performing Bots</h2>
          <button
            onClick={() => onTabChange('bots')}
            className="text-purple-600 hover:text-purple-700 text-xs md:text-sm font-bold transition-colors"
          >
            View All →
          </button>
        </div>

        <div className="grid gap-4 md:gap-6 md:grid-cols-3">
          {topBots.map((bot, index) => (
            <div
              key={bot.id}
              className="glass-effect rounded-xl md:rounded-2xl p-4 md:p-6 hover:shadow-glow transition-all-smooth transform hover:scale-105 cursor-pointer"
              onClick={() => onBotClick(bot.id)}
            >
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-bold text-gray-900 text-sm md:text-base">{bot.name}</h3>
                <div className={`px-2 py-1 rounded-full text-xs font-bold ${
                  bot.risk_level === 'low' ? 'bg-green-100 text-green-600' :
                  bot.risk_level === 'medium' ? 'bg-yellow-100 text-yellow-600' :
                  'bg-red-100 text-red-600'
                }`}>
                  {bot.risk_level.toUpperCase()}
                </div>
              </div>

              <div className="space-y-2 mb-4">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">30-Day Return</span>
                  <span className="font-bold text-green-600">
                    {formatPercentage(bot.performance.last_30_days_return)}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Win Rate</span>
                  <span className="font-bold text-blue-600">
                    {formatPercentage(bot.performance.win_rate, false)}
                  </span>
                </div>
              </div>

              <button className="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold py-2 rounded-lg text-sm hover:from-purple-600 hover:to-pink-600 transition-all-smooth">
                <FontAwesomeIcon icon={ICON_NAMES.PLAY} className="mr-2" />
                Invest Now
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Active Bot Investments */}
      <div className="glass-effect rounded-2xl md:rounded-3xl p-4 md:p-6">
        <div className="flex items-center justify-between mb-4 md:mb-6">
          <h3 className="text-lg md:text-xl font-bold text-gray-900">Active Investments</h3>
          <button
            onClick={() => onTabChange('portfolio')}
            className="text-purple-600 hover:text-purple-700 text-xs md:text-sm font-bold transition-colors"
          >
            View All →
          </button>
        </div>

        <div className="space-y-3 md:space-y-4">
          {recentActivities.length > 0 ? (
            recentActivities.map((investment, index) => (
              <div
                key={investment.id}
                className={`flex items-center justify-between py-3 md:py-4 ${
                  index < recentActivities.length - 1 ? 'border-b border-gray-100' : ''
                }`}
              >
                <div className="flex items-center space-x-3 md:space-x-4">
                  <div className="w-10 h-10 md:w-12 md:h-12 bg-purple-100 rounded-xl md:rounded-2xl flex items-center justify-center">
                    <FontAwesomeIcon icon={ICON_NAMES.ROBOT} className="text-purple-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900 text-sm md:text-base">
                      {investment.bot.name}
                    </div>
                    <div className="text-xs md:text-sm text-gray-500 font-medium">
                      Invested {formatDate(investment.start_date)}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-sm md:text-lg text-gray-900">
                    {formatCurrency(investment.current_value)}
                  </div>
                  <div className={`text-xs font-medium ${
                    investment.profit_loss >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {investment.profit_loss >= 0 ? '+' : ''}{formatCurrency(investment.profit_loss)}
                    ({formatPercentage(investment.profit_loss_percentage)})
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8">
              <div className="text-gray-400 text-4xl mb-2">
                <FontAwesomeIcon icon={ICON_NAMES.ROBOT} />
              </div>
              <p className="text-gray-500 text-sm">No active investments</p>
              <p className="text-gray-400 text-xs">Start investing in trading bots to see your portfolio here</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};