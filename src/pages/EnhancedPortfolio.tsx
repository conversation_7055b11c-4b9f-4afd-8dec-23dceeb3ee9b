/**
 * ==============================================
 * ENHANCED PORTFOLIO PAGE
 * ==============================================
 * 
 * Complete portfolio page showing bot investments, crypto balances,
 * and comprehensive performance metrics for the memecoin trading platform.
 */

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';
import { colors, components, animations } from '../lib/designSystem';
import { cryptoIcons, botIllustrations, assetUtils } from '../lib/cryptoAssets';
import { formatCurrency, formatPercentage } from '../lib/utils';


interface BotInvestment {
  id: string;
  botName: string;
  riskLevel: 'low' | 'medium' | 'high';
  investedAmount: number;
  currentValue: number;
  totalReturn: number;
  returnPercent: number;
  dailyReturn: number;
  isActive: boolean;
  investmentDate: string;
}

interface CryptoHolding {
  symbol: string;
  name: string;
  amount: number;
  value: number;
  change24h: number;
  changePercent24h: number;
}

interface PortfolioData {
  totalValue: number;
  totalChange24h: number;
  totalChangePercent24h: number;
  botInvestments: BotInvestment[];
  cryptoHoldings: CryptoHolding[];
  availableBalance: number;
}

interface EnhancedPortfolioProps {
  onDepositClick: () => void;
  onWithdrawClick: () => void;
  onBotClick: (botId: string) => void;
}

export const EnhancedPortfolio: React.FC<EnhancedPortfolioProps> = ({
  onDepositClick,
  onWithdrawClick,
  onBotClick,
}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'bots' | 'crypto'>('overview');

  // Mock data - in real app this would come from API
  const portfolioData: PortfolioData = {
    totalValue: 25750.89,
    totalChange24h: 1847.23,
    totalChangePercent24h: 7.73,
    availableBalance: 2450.50,
    botInvestments: [
      {
        id: '1',
        botName: 'Conservative Growth',
        riskLevel: 'low',
        investedAmount: 5000,
        currentValue: 5625.50,
        totalReturn: 625.50,
        returnPercent: 12.51,
        dailyReturn: 45.20,
        isActive: true,
        investmentDate: '2024-01-15',
      },
      {
        id: '2',
        botName: 'Momentum Hunter',
        riskLevel: 'medium',
        investedAmount: 8000,
        currentValue: 10240.75,
        totalReturn: 2240.75,
        returnPercent: 28.01,
        dailyReturn: 156.80,
        isActive: true,
        investmentDate: '2024-02-01',
      },
      {
        id: '3',
        botName: 'Alpha Seeker',
        riskLevel: 'high',
        investedAmount: 3000,
        currentValue: 4185.25,
        totalReturn: 1185.25,
        returnPercent: 39.51,
        dailyReturn: 89.45,
        isActive: true,
        investmentDate: '2024-02-20',
      },
    ],
    cryptoHoldings: [
      {
        symbol: 'USDT',
        name: 'Tether',
        amount: 2450.50,
        value: 2450.50,
        change24h: 0,
        changePercent24h: 0,
      },
      {
        symbol: 'USDC',
        name: 'USD Coin',
        amount: 1500.00,
        value: 1500.00,
        change24h: 0,
        changePercent24h: 0,
      },
      {
        symbol: 'BTC',
        name: 'Bitcoin',
        amount: 0.05,
        value: 3248.89,
        change24h: 248.89,
        changePercent24h: 8.29,
      },
      {
        symbol: 'ETH',
        name: 'Ethereum',
        amount: 0.5,
        value: 1500.00,
        change24h: 120.00,
        changePercent24h: 8.70,
      },
    ],
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-blue-600 bg-blue-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getRiskGradient = (risk: string) => {
    switch (risk) {
      case 'low': return 'from-green-500 to-emerald-500';
      case 'medium': return 'from-blue-500 to-cyan-500';
      case 'high': return 'from-red-500 to-pink-500';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  const totalBotValue = portfolioData.botInvestments.reduce((sum, bot) => sum + bot.currentValue, 0);
  const totalCryptoValue = portfolioData.cryptoHoldings.reduce((sum, crypto) => sum + crypto.value, 0);
  const totalBotReturns = portfolioData.botInvestments.reduce((sum, bot) => sum + bot.totalReturn, 0);

  return (
    <div className="p-4 md:p-6 pb-32 md:pb-6 space-y-6 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
      {/* Portfolio Overview */}
      <div className="bg-white rounded-3xl p-6 border border-gray-100 shadow-lg">
        <div className="text-center mb-6">
          <h1 className="text-2xl md:text-3xl font-black text-gray-900 mb-2">Portfolio Overview</h1>
          <div className="text-4xl md:text-5xl font-black text-gray-900 mb-2">
            {formatCurrency(portfolioData.totalValue)}
          </div>
          <div className={`text-lg font-semibold ${
            portfolioData.totalChange24h >= 0 ? 'text-green-600' : 'text-red-600'
          }`}>
            {portfolioData.totalChange24h >= 0 ? '+' : ''}{formatCurrency(portfolioData.totalChange24h)}
            ({formatPercentage(portfolioData.totalChangePercent24h)}) 24h
          </div>
        </div>

        {/* Portfolio Breakdown */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-white/50 rounded-2xl p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{formatCurrency(totalBotValue)}</div>
            <div className="text-sm text-gray-600">Bot Investments</div>
            <div className="text-xs text-green-600 font-medium">
              +{formatCurrency(totalBotReturns)} profit
            </div>
          </div>
          <div className="bg-white/50 rounded-2xl p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{formatCurrency(totalCryptoValue)}</div>
            <div className="text-sm text-gray-600">Crypto Holdings</div>
            <div className="text-xs text-gray-500">
              {portfolioData.cryptoHoldings.length} assets
            </div>
          </div>
          <div className="bg-white/50 rounded-2xl p-4 text-center">
            <div className="text-2xl font-bold text-gray-900">{formatCurrency(portfolioData.availableBalance)}</div>
            <div className="text-sm text-gray-600">Available Balance</div>
            <div className="text-xs text-gray-500">Ready to invest</div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-2xl p-2 shadow-lg border border-gray-100">
        <div className="grid grid-cols-3 gap-2">
          {[
            { key: 'overview', label: 'Overview', icon: ICON_NAMES.HOME },
            { key: 'bots', label: 'Bot Investments', icon: ICON_NAMES.ROBOT },
            { key: 'crypto', label: 'Crypto Holdings', icon: ICON_NAMES.CRYPTO },
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`py-3 px-4 rounded-xl font-semibold transition-all flex items-center justify-center space-x-2 ${
                activeTab === tab.key
                  ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <FontAwesomeIcon icon={tab.icon} className="text-sm" />
              <span className="hidden sm:inline">{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Quick Actions */}
          <div className="grid grid-cols-2 gap-4">
            <button 
              onClick={onDepositClick}
              className="glass-effect rounded-2xl p-6 border border-white/20 hover:shadow-lg transition-all group"
            >
              <div className="text-center">
                <FontAwesomeIcon icon={ICON_NAMES.DEPOSIT} className="text-3xl text-green-500 mb-3 group-hover:scale-110 transition-transform" />
                <div className="font-semibold text-gray-900">Deposit</div>
                <div className="text-sm text-gray-600">Add funds to invest</div>
              </div>
            </button>
            <button 
              onClick={onWithdrawClick}
              className="glass-effect rounded-2xl p-6 border border-white/20 hover:shadow-lg transition-all group"
            >
              <div className="text-center">
                <FontAwesomeIcon icon={ICON_NAMES.WITHDRAW} className="text-3xl text-red-500 mb-3 group-hover:scale-110 transition-transform" />
                <div className="font-semibold text-gray-900">Withdraw</div>
                <div className="text-sm text-gray-600">Cash out profits</div>
              </div>
            </button>
          </div>

          {/* Performance Summary */}
          <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-3xl p-6 border border-purple-100">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Performance Summary</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  +{formatPercentage(totalBotReturns / portfolioData.botInvestments.reduce((sum, bot) => sum + bot.investedAmount, 0) * 100)}
                </div>
                <div className="text-sm text-gray-600">Total Bot Returns</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {portfolioData.botInvestments.filter(bot => bot.isActive).length}
                </div>
                <div className="text-sm text-gray-600">Active Bots</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  +{formatCurrency(portfolioData.botInvestments.reduce((sum, bot) => sum + bot.dailyReturn, 0))}
                </div>
                <div className="text-sm text-gray-600">Daily Earnings</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'bots' && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-bold text-gray-900">Your Bot Investments</h3>
            <div className="text-sm text-gray-600">
              Total: {formatCurrency(totalBotValue)}
            </div>
          </div>
          
          {portfolioData.botInvestments.map((bot) => (
            <div
              key={bot.id}
              className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all cursor-pointer"
              onClick={() => onBotClick(bot.id)}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div 
                    className="w-12 h-12"
                    dangerouslySetInnerHTML={{ 
                      __html: assetUtils.getBotIllustration(bot.riskLevel) 
                    }}
                  />
                  <div>
                    <h4 className="text-lg font-bold text-gray-900">{bot.botName}</h4>
                    <span className={`px-2 py-1 rounded-lg text-xs font-medium ${getRiskColor(bot.riskLevel)}`}>
                      {bot.riskLevel.toUpperCase()} RISK
                    </span>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-gray-900">
                    {formatCurrency(bot.currentValue)}
                  </div>
                  <div className="text-sm text-green-600 font-medium">
                    +{formatCurrency(bot.totalReturn)} ({formatPercentage(bot.returnPercent)})
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Invested:</span>
                  <div className="font-medium">{formatCurrency(bot.investedAmount)}</div>
                </div>
                <div>
                  <span className="text-gray-600">Daily Return:</span>
                  <div className="font-medium text-green-600">+{formatCurrency(bot.dailyReturn)}</div>
                </div>
                <div>
                  <span className="text-gray-600">Status:</span>
                  <div className={`font-medium ${bot.isActive ? 'text-green-600' : 'text-gray-500'}`}>
                    {bot.isActive ? 'Active' : 'Inactive'}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'crypto' && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-bold text-gray-900">Crypto Holdings</h3>
            <div className="text-sm text-gray-600">
              Total: {formatCurrency(totalCryptoValue)}
            </div>
          </div>
          
          {portfolioData.cryptoHoldings.map((crypto) => (
            <div
              key={crypto.symbol}
              className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div 
                    className="w-12 h-12 flex items-center justify-center"
                    dangerouslySetInnerHTML={{ 
                      __html: assetUtils.getCryptoIcon(crypto.symbol.toLowerCase()) 
                    }}
                  />
                  <div>
                    <div className="text-lg font-bold text-gray-900">{crypto.name}</div>
                    <div className="text-sm text-gray-600">{crypto.amount} {crypto.symbol}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-gray-900">
                    {formatCurrency(crypto.value)}
                  </div>
                  <div className={`text-sm font-medium ${
                    crypto.change24h >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {crypto.change24h >= 0 ? '+' : ''}{formatCurrency(crypto.change24h)} 
                    ({formatPercentage(crypto.changePercent24h)})
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
