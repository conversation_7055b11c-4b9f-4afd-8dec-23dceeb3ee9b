-- ==============================================
-- DEMO DATA FOR MOREWISE PLATFORM
-- ==============================================

-- Insert demo users with referral codes
INSERT INTO users (id, email, username, name, is_verified, kyc_status, referral_code, preferences) VALUES 
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'johndoe', '<PERSON>', true, 'approved', 'JOHN2024', '{"risk_tolerance": "medium", "notifications": {"email": true, "push": true}, "theme": "dark", "currency": "USD"}'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'jane<PERSON>', '<PERSON>', true, 'approved', 'JANE2024', '{"risk_tolerance": "high", "notifications": {"email": true, "push": false}, "theme": "dark", "currency": "USD"}'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'mikewilson', 'Mike Wilson', false, 'pending', 'MIKE2024', '{"risk_tolerance": "low", "notifications": {"email": false, "push": true}, "theme": "light", "currency": "USD"}'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'sarahjones', 'Sarah Jones', true, 'approved', 'SARAH2024', '{"risk_tolerance": "medium", "notifications": {"email": true, "push": true}, "theme": "dark", "currency": "EUR"}'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'alexbrown', 'Alex Brown', true, 'rejected', 'ALEX2024', '{"risk_tolerance": "high", "notifications": {"email": true, "push": true}, "theme": "dark", "currency": "USD"}');

-- Insert referral relationships
INSERT INTO referrals (referrer_id, referred_id, referral_code, commission_rate, total_commission, status) VALUES 
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'JOHN2024', 0.05, 125.50, 'active'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'JOHN2024', 0.05, 45.25, 'active'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'SARAH2024', 0.05, 89.75, 'active');

-- Update users with referral relationships
UPDATE users SET referred_by = '550e8400-e29b-41d4-a716-************' WHERE id = '550e8400-e29b-41d4-a716-************';
UPDATE users SET referred_by = '550e8400-e29b-41d4-a716-************' WHERE id = '550e8400-e29b-41d4-a716-************';
UPDATE users SET referred_by = '550e8400-e29b-41d4-a716-************' WHERE id = '550e8400-e29b-41d4-a716-************';

-- Insert portfolios for demo users
INSERT INTO portfolios (user_id, total_balance, available_balance, invested_amount, total_profit_loss, currency) VALUES 
('550e8400-e29b-41d4-a716-************', 15750.25, 8250.25, 7500.00, 2250.25, 'USD'),
('550e8400-e29b-41d4-a716-************', 28900.50, 12400.50, 16500.00, 4900.50, 'USD'),
('550e8400-e29b-41d4-a716-************', 5250.75, 3250.75, 2000.00, -150.25, 'USD'),
('550e8400-e29b-41d4-a716-************', 42300.80, 18300.80, 24000.00, 8300.80, 'EUR'),
('550e8400-e29b-41d4-a716-************', 1850.00, 850.00, 1000.00, -150.00, 'USD');

-- Insert bot investments
INSERT INTO bot_investments (user_id, bot_id, amount, status, profit_loss, start_date) VALUES 
('550e8400-e29b-41d4-a716-************', (SELECT id FROM trading_bots WHERE name = 'MoonShiba Bot'), 5000.00, 'active', 1250.25, NOW() - INTERVAL '30 days'),
('550e8400-e29b-41d4-a716-************', (SELECT id FROM trading_bots WHERE name = 'Arbitrage Master'), 2500.00, 'active', 450.50, NOW() - INTERVAL '15 days'),
('550e8400-e29b-41d4-a716-************', (SELECT id FROM trading_bots WHERE name = 'MoonShiba Bot'), 10000.00, 'active', 2850.75, NOW() - INTERVAL '45 days'),
('550e8400-e29b-41d4-a716-************', (SELECT id FROM trading_bots WHERE name = 'Arbitrage Master'), 6500.00, 'active', 1180.25, NOW() - INTERVAL '20 days'),
('550e8400-e29b-41d4-a716-************', (SELECT id FROM trading_bots WHERE name = 'Arbitrage Master'), 2000.00, 'paused', -150.25, NOW() - INTERVAL '10 days'),
('550e8400-e29b-41d4-a716-************', (SELECT id FROM trading_bots WHERE name = 'MoonShiba Bot'), 15000.00, 'active', 4250.80, NOW() - INTERVAL '60 days'),
('550e8400-e29b-41d4-a716-************', (SELECT id FROM trading_bots WHERE name = 'Arbitrage Master'), 9000.00, 'active', 1650.40, NOW() - INTERVAL '25 days'),
('550e8400-e29b-41d4-a716-************', (SELECT id FROM trading_bots WHERE name = 'MoonShiba Bot'), 1000.00, 'stopped', -150.00, NOW() - INTERVAL '5 days');

-- Insert demo trades
INSERT INTO trades (bot_investment_id, bot_id, user_id, trade_type, coin_symbol, amount, price, total_value, fees, profit_loss, status, executed_at) VALUES 
((SELECT id FROM bot_investments WHERE user_id = '550e8400-e29b-41d4-a716-************' AND bot_id = (SELECT id FROM trading_bots WHERE name = 'MoonShiba Bot')), 
 (SELECT id FROM trading_bots WHERE name = 'MoonShiba Bot'), '550e8400-e29b-41d4-a716-************', 'buy', 'SHIB', 1000000.00, 0.000025, 25.00, 0.05, NULL, 'completed', NOW() - INTERVAL '2 hours'),
((SELECT id FROM bot_investments WHERE user_id = '550e8400-e29b-41d4-a716-************' AND bot_id = (SELECT id FROM trading_bots WHERE name = 'MoonShiba Bot')), 
 (SELECT id FROM trading_bots WHERE name = 'MoonShiba Bot'), '550e8400-e29b-41d4-a716-************', 'sell', 'SHIB', 500000.00, 0.000028, 14.00, 0.03, 1.42, 'completed', NOW() - INTERVAL '1 hour'),
((SELECT id FROM bot_investments WHERE user_id = '550e8400-e29b-41d4-a716-************' AND bot_id = (SELECT id FROM trading_bots WHERE name = 'Arbitrage Master')), 
 (SELECT id FROM trading_bots WHERE name = 'Arbitrage Master'), '550e8400-e29b-41d4-a716-************', 'buy', 'DOGE', 5000.00, 0.08, 400.00, 0.80, NULL, 'completed', NOW() - INTERVAL '3 hours'),
((SELECT id FROM bot_investments WHERE user_id = '550e8400-e29b-41d4-a716-************' AND bot_id = (SELECT id FROM trading_bots WHERE name = 'Arbitrage Master')), 
 (SELECT id FROM trading_bots WHERE name = 'Arbitrage Master'), '550e8400-e29b-41d4-a716-************', 'sell', 'DOGE', 5000.00, 0.082, 410.00, 0.82, 9.38, 'completed', NOW() - INTERVAL '2 hours');

-- Insert wallet transactions
INSERT INTO wallet_transactions (user_id, transaction_type, amount, currency, status, description, created_at) VALUES 
('550e8400-e29b-41d4-a716-************', 'deposit', 10000.00, 'USD', 'completed', 'Bank transfer deposit', NOW() - INTERVAL '35 days'),
('550e8400-e29b-41d4-a716-************', 'deposit', 5000.00, 'USD', 'completed', 'Credit card deposit', NOW() - INTERVAL '20 days'),
('550e8400-e29b-41d4-a716-************', 'profit', 1250.25, 'USD', 'completed', 'Trading profit from MoonShiba Bot', NOW() - INTERVAL '5 days'),
('550e8400-e29b-41d4-a716-************', 'deposit', 20000.00, 'USD', 'completed', 'Wire transfer deposit', NOW() - INTERVAL '50 days'),
('550e8400-e29b-41d4-a716-************', 'deposit', 8000.00, 'USD', 'completed', 'Crypto deposit', NOW() - INTERVAL '30 days'),
('550e8400-e29b-41d4-a716-************', 'profit', 2850.75, 'USD', 'completed', 'Trading profit from MoonShiba Bot', NOW() - INTERVAL '10 days'),
('550e8400-e29b-41d4-a716-************', 'withdrawal', 2000.00, 'USD', 'completed', 'Withdrawal to bank account', NOW() - INTERVAL '3 days'),
('550e8400-e29b-41d4-a716-************', 'deposit', 5000.00, 'USD', 'completed', 'Initial deposit', NOW() - INTERVAL '12 days'),
('550e8400-e29b-41d4-a716-************', 'loss', 150.25, 'USD', 'completed', 'Trading loss from Arbitrage Master', NOW() - INTERVAL '2 days'),
('550e8400-e29b-41d4-a716-************', 'deposit', 35000.00, 'EUR', 'completed', 'Large wire transfer', NOW() - INTERVAL '65 days'),
('550e8400-e29b-41d4-a716-************', 'profit', 4250.80, 'EUR', 'completed', 'Trading profit from MoonShiba Bot', NOW() - INTERVAL '15 days'),
('550e8400-e29b-41d4-a716-************', 'deposit', 2000.00, 'USD', 'completed', 'Initial deposit', NOW() - INTERVAL '6 days'),
('550e8400-e29b-41d4-a716-************', 'loss', 150.00, 'USD', 'completed', 'Trading loss from MoonShiba Bot', NOW() - INTERVAL '1 day');

-- Insert audit logs for admin actions
INSERT INTO audit_logs (admin_id, action, table_name, record_id, new_values, ip_address, user_agent, created_at) VALUES 
((SELECT id FROM admin_users WHERE email = '<EMAIL>'), 'USER_KYC_APPROVED', 'users', '550e8400-e29b-41d4-a716-************', '{"kyc_status": "approved"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL '30 days'),
((SELECT id FROM admin_users WHERE email = '<EMAIL>'), 'USER_KYC_APPROVED', 'users', '550e8400-e29b-41d4-a716-************', '{"kyc_status": "approved"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL '25 days'),
((SELECT id FROM admin_users WHERE email = '<EMAIL>'), 'USER_KYC_REJECTED', 'users', '550e8400-e29b-41d4-a716-************', '{"kyc_status": "rejected"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL '10 days'),
((SELECT id FROM admin_users WHERE email = '<EMAIL>'), 'BOT_CREATED', 'trading_bots', (SELECT id FROM trading_bots WHERE name = 'MoonShiba Bot'), '{"name": "MoonShiba Bot", "is_active": true}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL '60 days'),
((SELECT id FROM admin_users WHERE email = '<EMAIL>'), 'BOT_CREATED', 'trading_bots', (SELECT id FROM trading_bots WHERE name = 'Arbitrage Master'), '{"name": "Arbitrage Master", "is_active": true}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL '55 days');

-- Insert live support chat messages (demo)
CREATE TABLE IF NOT EXISTS support_chats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    admin_id UUID REFERENCES admin_users(id),
    status VARCHAR(20) DEFAULT 'open' CHECK (status IN ('open', 'closed', 'pending')),
    priority VARCHAR(10) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    subject VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS support_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    chat_id UUID REFERENCES support_chats(id) ON DELETE CASCADE,
    sender_id UUID, -- Can be user_id or admin_id
    sender_type VARCHAR(10) CHECK (sender_type IN ('user', 'admin')),
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert demo support chats
INSERT INTO support_chats (id, user_id, admin_id, status, priority, subject) VALUES 
('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', (SELECT id FROM admin_users WHERE email = '<EMAIL>'), 'open', 'medium', 'Question about trading bot performance'),
('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', NULL, 'pending', 'high', 'Withdrawal request stuck'),
('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', (SELECT id FROM admin_users WHERE email = '<EMAIL>'), 'closed', 'low', 'Account verification completed');

-- Insert demo support messages
INSERT INTO support_messages (chat_id, sender_id, sender_type, message, is_read, created_at) VALUES 
('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'user', 'Hi, I have a question about my MoonShiba Bot performance. It seems to be underperforming compared to the advertised returns.', true, NOW() - INTERVAL '2 hours'),
('660e8400-e29b-41d4-a716-************', (SELECT id FROM admin_users WHERE email = '<EMAIL>'), 'admin', 'Hello John! Thank you for reaching out. Let me check your bot performance data. The MoonShiba Bot has been performing within expected parameters, but market conditions have been challenging lately.', true, NOW() - INTERVAL '1 hour 30 minutes'),
('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'user', 'I understand. Should I consider switching to a different strategy or wait it out?', false, NOW() - INTERVAL '1 hour'),
('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'user', 'My withdrawal request has been pending for 3 days. When will it be processed?', false, NOW() - INTERVAL '4 hours'),
('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'user', 'Thank you for approving my account verification!', true, NOW() - INTERVAL '1 day'),
('660e8400-e29b-41d4-a716-************', (SELECT id FROM admin_users WHERE email = '<EMAIL>'), 'admin', 'You are welcome, Sarah! Your account is now fully verified and you can access all platform features.', true, NOW() - INTERVAL '23 hours');

-- Add triggers for support tables
CREATE TRIGGER update_support_chats_updated_at BEFORE UPDATE ON support_chats FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add indexes for support tables
CREATE INDEX idx_support_chats_user_id ON support_chats(user_id);
CREATE INDEX idx_support_chats_admin_id ON support_chats(admin_id);
CREATE INDEX idx_support_chats_status ON support_chats(status);
CREATE INDEX idx_support_messages_chat_id ON support_messages(chat_id);
CREATE INDEX idx_support_messages_created_at ON support_messages(created_at);
