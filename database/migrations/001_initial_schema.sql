-- ==============================================
-- MOREWISE CRYPTO TRADING PLATFORM
-- Initial Database Schema Migration
-- ==============================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- ==============================================
-- USERS TABLE
-- ==============================================
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    avatar_url TEXT,
    wallet_address VARCHAR(42),
    is_verified BOOLEAN DEFAULT FALSE,
    kyc_status VARCHAR(20) DEFAULT 'pending' CHECK (kyc_status IN ('pending', 'approved', 'rejected')),
    referral_code VARCHAR(20) UNIQUE,
    referred_by UUID REFERENCES users(id),
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==============================================
-- ADMIN USERS TABLE
-- ==============================================
CREATE TABLE admin_users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    role VARCHAR(20) DEFAULT 'admin' CHECK (role IN ('super_admin', 'admin', 'moderator')),
    permissions JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==============================================
-- TRADING BOTS TABLE
-- ==============================================
CREATE TABLE trading_bots (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    strategy JSONB NOT NULL,
    target_coins TEXT[] DEFAULT '{}',
    risk_level VARCHAR(10) CHECK (risk_level IN ('low', 'medium', 'high')),
    min_investment DECIMAL(15,2) DEFAULT 0,
    max_investment DECIMAL(15,2),
    performance JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_by UUID REFERENCES admin_users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==============================================
-- USER PORTFOLIOS TABLE
-- ==============================================
CREATE TABLE portfolios (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    total_balance DECIMAL(15,2) DEFAULT 0,
    available_balance DECIMAL(15,2) DEFAULT 0,
    invested_amount DECIMAL(15,2) DEFAULT 0,
    total_profit_loss DECIMAL(15,2) DEFAULT 0,
    currency VARCHAR(10) DEFAULT 'USD',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==============================================
-- BOT INVESTMENTS TABLE
-- ==============================================
CREATE TABLE bot_investments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    bot_id UUID REFERENCES trading_bots(id) ON DELETE CASCADE,
    amount DECIMAL(15,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'paused', 'stopped', 'completed')),
    profit_loss DECIMAL(15,2) DEFAULT 0,
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==============================================
-- TRADES TABLE
-- ==============================================
CREATE TABLE trades (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bot_investment_id UUID REFERENCES bot_investments(id) ON DELETE CASCADE,
    bot_id UUID REFERENCES trading_bots(id),
    user_id UUID REFERENCES users(id),
    trade_type VARCHAR(10) CHECK (trade_type IN ('buy', 'sell')),
    coin_symbol VARCHAR(20) NOT NULL,
    amount DECIMAL(15,8) NOT NULL,
    price DECIMAL(15,8) NOT NULL,
    total_value DECIMAL(15,2) NOT NULL,
    fees DECIMAL(15,8) DEFAULT 0,
    profit_loss DECIMAL(15,2),
    status VARCHAR(20) DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==============================================
-- WALLET TRANSACTIONS TABLE
-- ==============================================
CREATE TABLE wallet_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    transaction_type VARCHAR(20) CHECK (transaction_type IN ('deposit', 'withdrawal', 'transfer', 'fee', 'profit', 'loss')),
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'USD',
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    description TEXT,
    external_tx_id VARCHAR(255),
    wallet_address VARCHAR(42),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==============================================
-- REFERRALS TABLE
-- ==============================================
CREATE TABLE referrals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    referrer_id UUID REFERENCES users(id) ON DELETE CASCADE,
    referred_id UUID REFERENCES users(id) ON DELETE CASCADE,
    referral_code VARCHAR(20) NOT NULL,
    commission_rate DECIMAL(5,4) DEFAULT 0.05,
    total_commission DECIMAL(15,2) DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'expired')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==============================================
-- SYSTEM SETTINGS TABLE
-- ==============================================
CREATE TABLE system_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(100) UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==============================================
-- AUDIT LOGS TABLE
-- ==============================================
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID,
    admin_id UUID REFERENCES admin_users(id),
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(50),
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==============================================
-- INDEXES FOR PERFORMANCE
-- ==============================================
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_referral_code ON users(referral_code);
CREATE INDEX idx_portfolios_user_id ON portfolios(user_id);
CREATE INDEX idx_bot_investments_user_id ON bot_investments(user_id);
CREATE INDEX idx_bot_investments_bot_id ON bot_investments(bot_id);
CREATE INDEX idx_trades_user_id ON trades(user_id);
CREATE INDEX idx_trades_bot_id ON trades(bot_id);
CREATE INDEX idx_trades_executed_at ON trades(executed_at);
CREATE INDEX idx_wallet_transactions_user_id ON wallet_transactions(user_id);
CREATE INDEX idx_wallet_transactions_created_at ON wallet_transactions(created_at);
CREATE INDEX idx_referrals_referrer_id ON referrals(referrer_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX idx_audit_logs_admin_id ON audit_logs(admin_id);

-- ==============================================
-- TRIGGERS FOR UPDATED_AT
-- ==============================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_admin_users_updated_at BEFORE UPDATE ON admin_users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_trading_bots_updated_at BEFORE UPDATE ON trading_bots FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_portfolios_updated_at BEFORE UPDATE ON portfolios FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bot_investments_updated_at BEFORE UPDATE ON bot_investments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_wallet_transactions_updated_at BEFORE UPDATE ON wallet_transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_referrals_updated_at BEFORE UPDATE ON referrals FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_system_settings_updated_at BEFORE UPDATE ON system_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ==============================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- ==============================================
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE portfolios ENABLE ROW LEVEL SECURITY;
ALTER TABLE bot_investments ENABLE ROW LEVEL SECURITY;
ALTER TABLE trades ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE referrals ENABLE ROW LEVEL SECURITY;

-- Users can only see their own data
CREATE POLICY "Users can view own data" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own data" ON users FOR UPDATE USING (auth.uid() = id);

-- Portfolio policies
CREATE POLICY "Users can view own portfolio" ON portfolios FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own portfolio" ON portfolios FOR UPDATE USING (auth.uid() = user_id);

-- Bot investment policies
CREATE POLICY "Users can view own investments" ON bot_investments FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own investments" ON bot_investments FOR ALL USING (auth.uid() = user_id);

-- Trade policies
CREATE POLICY "Users can view own trades" ON trades FOR SELECT USING (auth.uid() = user_id);

-- Wallet transaction policies
CREATE POLICY "Users can view own transactions" ON wallet_transactions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create own transactions" ON wallet_transactions FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Referral policies
CREATE POLICY "Users can view own referrals" ON referrals FOR SELECT USING (auth.uid() = referrer_id OR auth.uid() = referred_id);

-- ==============================================
-- INITIAL DATA
-- ==============================================

-- No default admin user - first login creates the admin

-- Insert default system settings
INSERT INTO system_settings (key, value, description, is_public) VALUES 
('platform_name', '"MemeBot Pro"', 'Platform display name', true),
('maintenance_mode', 'false', 'Enable/disable maintenance mode', false),
('referral_commission_rate', '0.05', 'Default referral commission rate (5%)', false),
('min_deposit_amount', '10.00', 'Minimum deposit amount in USD', true),
('max_daily_trades', '100', 'Maximum trades per day per user', false);

-- Insert demo trading bots
INSERT INTO trading_bots (name, description, strategy, target_coins, risk_level, min_investment, max_investment, performance, created_by) VALUES 
('MoonShiba Bot', 'Specialized in Shiba Inu and similar meme coins with momentum trading strategy', 
 '{"type": "momentum", "parameters": {"momentum_threshold": 0.05, "volume_threshold": 1000000}, "stop_loss_percentage": 5, "take_profit_percentage": 15, "max_trades_per_day": 10}',
 '{"SHIB", "DOGE", "PEPE"}', 'medium', 100.00, 10000.00,
 '{"total_return_percentage": 24.5, "win_rate": 68, "total_trades": 156, "profitable_trades": 106, "average_trade_duration": 4.2, "max_drawdown": 8.3, "sharpe_ratio": 1.8, "last_30_days_return": 12.3, "last_7_days_return": 3.7}',
 (SELECT id FROM admin_users WHERE email = '<EMAIL>')),

('Arbitrage Master', 'Cross-exchange arbitrage opportunities for maximum profit with minimal risk',
 '{"type": "arbitrage", "parameters": {"min_spread": 0.02, "max_slippage": 0.01}, "stop_loss_percentage": 2, "take_profit_percentage": 5, "max_trades_per_day": 50}',
 '{"DOGE", "SHIB", "FLOKI", "BONK"}', 'low', 500.00, 50000.00,
 '{"total_return_percentage": 18.2, "win_rate": 89, "total_trades": 342, "profitable_trades": 304, "average_trade_duration": 0.8, "max_drawdown": 3.1, "sharpe_ratio": 2.4, "last_30_days_return": 8.9, "last_7_days_return": 2.1}',
 (SELECT id FROM admin_users WHERE email = '<EMAIL>'));
