-- ==============================================
-- MULTI-WALLET SYSTEM FOR CRYPTO DEPOSITS
-- ==============================================

-- Supported cryptocurrencies table
CREATE TABLE supported_cryptocurrencies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(50) NOT NULL,
    network VARCHAR(50) NOT NULL,
    contract_address VARCHAR(42),
    decimals INTEGER DEFAULT 18,
    is_active BOOLEAN DEFAULT TRUE,
    min_deposit DECIMAL(15,8) DEFAULT 0,
    deposit_fee_percentage DECIMAL(5,4) DEFAULT 0,
    icon_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Wallet bundles (groups of wallets for different cryptocurrencies)
CREATE TABLE wallet_bundles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by UUID REFERENCES admin_users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Individual wallets within bundles
CREATE TABLE crypto_wallets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bundle_id UUID REFERENCES wallet_bundles(id) ON DELETE CASCADE,
    cryptocurrency_id UUID REFERENCES supported_cryptocurrencies(id),
    address VARCHAR(100) NOT NULL,
    private_key_encrypted TEXT, -- Encrypted private key (admin only)
    is_active BOOLEAN DEFAULT TRUE,
    created_by UUID REFERENCES admin_users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(bundle_id, cryptocurrency_id)
);

-- User wallet assignments (each user gets assigned a wallet bundle)
CREATE TABLE user_wallet_assignments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    bundle_id UUID REFERENCES wallet_bundles(id),
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(user_id) -- Each user can only have one active bundle
);

-- Deposit transactions
CREATE TABLE deposit_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    wallet_id UUID REFERENCES crypto_wallets(id),
    cryptocurrency_id UUID REFERENCES supported_cryptocurrencies(id),
    amount DECIMAL(15,8) NOT NULL,
    transaction_hash VARCHAR(100),
    from_address VARCHAR(100),
    to_address VARCHAR(100) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirming', 'confirmed', 'failed', 'expired')),
    confirmations INTEGER DEFAULT 0,
    required_confirmations INTEGER DEFAULT 3,
    fee_amount DECIMAL(15,8) DEFAULT 0,
    net_amount DECIMAL(15,8) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE,
    confirmed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_crypto_wallets_bundle_id ON crypto_wallets(bundle_id);
CREATE INDEX idx_crypto_wallets_address ON crypto_wallets(address);
CREATE INDEX idx_user_wallet_assignments_user_id ON user_wallet_assignments(user_id);
CREATE INDEX idx_user_wallet_assignments_bundle_id ON user_wallet_assignments(bundle_id);
CREATE INDEX idx_deposit_transactions_user_id ON deposit_transactions(user_id);
CREATE INDEX idx_deposit_transactions_status ON deposit_transactions(status);
CREATE INDEX idx_deposit_transactions_transaction_hash ON deposit_transactions(transaction_hash);

-- Triggers for updated_at
CREATE TRIGGER update_supported_cryptocurrencies_updated_at BEFORE UPDATE ON supported_cryptocurrencies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_wallet_bundles_updated_at BEFORE UPDATE ON wallet_bundles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_crypto_wallets_updated_at BEFORE UPDATE ON crypto_wallets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_deposit_transactions_updated_at BEFORE UPDATE ON deposit_transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- RLS Policies
ALTER TABLE supported_cryptocurrencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_bundles ENABLE ROW LEVEL SECURITY;
ALTER TABLE crypto_wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_wallet_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE deposit_transactions ENABLE ROW LEVEL SECURITY;

-- Users can view supported cryptocurrencies
CREATE POLICY "Users can view supported cryptocurrencies" ON supported_cryptocurrencies FOR SELECT TO authenticated USING (is_active = true);

-- Users can view their assigned wallet bundle
CREATE POLICY "Users can view their wallet bundle" ON wallet_bundles FOR SELECT TO authenticated USING (
    id IN (SELECT bundle_id FROM user_wallet_assignments WHERE user_id = auth.uid() AND is_active = true)
);

-- Users can view wallets in their assigned bundle
CREATE POLICY "Users can view their assigned wallets" ON crypto_wallets FOR SELECT TO authenticated USING (
    bundle_id IN (SELECT bundle_id FROM user_wallet_assignments WHERE user_id = auth.uid() AND is_active = true)
);

-- Users can view their wallet assignments
CREATE POLICY "Users can view their wallet assignments" ON user_wallet_assignments FOR SELECT USING (auth.uid() = user_id);

-- Users can view and create their own deposit transactions
CREATE POLICY "Users can view own deposits" ON deposit_transactions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create own deposits" ON deposit_transactions FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Admin policies (full access)
CREATE POLICY "Admins can manage cryptocurrencies" ON supported_cryptocurrencies FOR ALL TO authenticated USING (
    EXISTS (SELECT 1 FROM admin_users WHERE admin_users.id = auth.uid() AND admin_users.is_active = true)
);

CREATE POLICY "Admins can manage wallet bundles" ON wallet_bundles FOR ALL TO authenticated USING (
    EXISTS (SELECT 1 FROM admin_users WHERE admin_users.id = auth.uid() AND admin_users.is_active = true)
);

CREATE POLICY "Admins can manage crypto wallets" ON crypto_wallets FOR ALL TO authenticated USING (
    EXISTS (SELECT 1 FROM admin_users WHERE admin_users.id = auth.uid() AND admin_users.is_active = true)
);

CREATE POLICY "Admins can manage wallet assignments" ON user_wallet_assignments FOR ALL TO authenticated USING (
    EXISTS (SELECT 1 FROM admin_users WHERE admin_users.id = auth.uid() AND admin_users.is_active = true)
);

CREATE POLICY "Admins can manage deposit transactions" ON deposit_transactions FOR ALL TO authenticated USING (
    EXISTS (SELECT 1 FROM admin_users WHERE admin_users.id = auth.uid() AND admin_users.is_active = true)
);

-- Insert default supported cryptocurrencies
INSERT INTO supported_cryptocurrencies (symbol, name, network, decimals, min_deposit, icon_url) VALUES 
('BTC', 'Bitcoin', 'Bitcoin', 8, 0.0001, 'https://cryptologos.cc/logos/bitcoin-btc-logo.png'),
('ETH', 'Ethereum', 'Ethereum', 18, 0.001, 'https://cryptologos.cc/logos/ethereum-eth-logo.png'),
('USDT', 'Tether USD', 'Ethereum', 6, 1.0, 'https://cryptologos.cc/logos/tether-usdt-logo.png'),
('USDC', 'USD Coin', 'Ethereum', 6, 1.0, 'https://cryptologos.cc/logos/usd-coin-usdc-logo.png'),
('BNB', 'Binance Coin', 'BSC', 18, 0.01, 'https://cryptologos.cc/logos/bnb-bnb-logo.png'),
('DOGE', 'Dogecoin', 'Dogecoin', 8, 1.0, 'https://cryptologos.cc/logos/dogecoin-doge-logo.png'),
('SHIB', 'Shiba Inu', 'Ethereum', 18, 1000000, 'https://cryptologos.cc/logos/shiba-inu-shib-logo.png'),
('PEPE', 'Pepe', 'Ethereum', 18, 1000000, 'https://cryptologos.cc/logos/pepe-pepe-logo.png');
