-- ==============================================
-- FIX USER INSERT POLICY
-- ==============================================
-- This migration adds the missing INSERT policy for users table
-- to allow new user registration through Supabase auth

-- Add INSERT policy for users table
-- Allow authenticated users to insert their own user record
CREATE POLICY "Users can insert own data" ON users FOR INSERT WITH CHECK (auth.uid() = id);

-- Add INSERT policy for portfolios table
-- Allow users to create their own portfolio
CREATE POLICY "Users can create own portfolio" ON portfolios FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Make trading_bots readable by all authenticated users
-- (they need to see available bots to invest in)
ALTER TABLE trading_bots ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Authenticated users can view trading bots" ON trading_bots FOR SELECT TO authenticated USING (is_active = true);

-- Allow admins to manage trading bots
CREATE POLICY "Admins can manage trading bots" ON trading_bots FOR ALL TO authenticated USING (
  EXISTS (
    SELECT 1 FROM admin_users 
    WHERE admin_users.id = auth.uid() 
    AND admin_users.is_active = true
  )
);

-- Add policy for referrals creation
CREATE POLICY "Users can create referrals" ON referrals FOR INSERT WITH CHECK (auth.uid() = referrer_id);
