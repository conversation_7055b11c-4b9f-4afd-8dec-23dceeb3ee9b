-- ==============================================
-- FIRST LOGIN ADMIN SYSTEM UPDATE
-- ==============================================
-- 
-- Update the admin system to allow first login to become admin

-- Add a system setting to track if admin has been initialized
INSERT INTO system_settings (key, value, description, is_public) VALUES 
('admin_initialized', 'false', 'Whether the first admin has been created', false)
ON CONFLICT (key) DO UPDATE SET value = 'false';

-- Remove the existing admin user if any (for clean setup)
DELETE FROM admin_users;

-- Remove the trading bots that reference admin users
DELETE FROM trading_bots;

-- Re-insert trading bots without admin reference (they'll be created by first admin)
INSERT INTO trading_bots (name, description, strategy, target_coins, risk_level, min_investment, max_investment, performance) VALUES 
('MoonShiba Bot', 'Specialized in Shiba Inu and similar meme coins with momentum trading strategy', 
 '{"type": "momentum", "parameters": {"momentum_threshold": 0.05, "volume_threshold": 1000000}, "stop_loss_percentage": 5, "take_profit_percentage": 15, "max_trades_per_day": 10}',
 '{"SHIB", "DOGE", "PEPE"}', 'medium', 100.00, 10000.00,
 '{"total_return_percentage": 24.5, "win_rate": 68, "total_trades": 156, "profitable_trades": 106, "average_trade_duration": 4.2, "max_drawdown": 8.3, "sharpe_ratio": 1.8, "last_30_days_return": 12.3, "last_7_days_return": 3.7}'),

('Arbitrage Master', 'Cross-exchange arbitrage opportunities for maximum profit with minimal risk',
 '{"type": "arbitrage", "parameters": {"min_spread": 0.02, "max_slippage": 0.01}, "stop_loss_percentage": 2, "take_profit_percentage": 5, "max_trades_per_day": 50}',
 '{"DOGE", "SHIB", "FLOKI", "BONK"}', 'low', 500.00, 50000.00,
 '{"total_return_percentage": 18.2, "win_rate": 89, "total_trades": 342, "profitable_trades": 304, "average_trade_duration": 0.8, "max_drawdown": 3.1, "sharpe_ratio": 2.4, "last_30_days_return": 8.9, "last_7_days_return": 2.1}');

-- Update trading_bots table to make created_by nullable
ALTER TABLE trading_bots ALTER COLUMN created_by DROP NOT NULL;
