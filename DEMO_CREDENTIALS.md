# 🚀 MemeBot Pro - Demo Account Credentials

## 📋 Demo Account Information

### 🔐 Login Credentials
- **Username:** `demo`
- **Password:** `demo123`
- **Email:** `<EMAIL>`

### 👤 Demo User Profile
- **Name:** <PERSON>
- **Wallet Address:** `0x742d35Cc6634C0532925a3b8D4C9db96590b5`
- **KYC Status:** Approved
- **Member Since:** January 2024

### 💰 Portfolio Overview
- **Total Portfolio Value:** $7,420.50
- **Total Invested:** $6,000.00
- **Total Profit/Loss:** +$1,420.50 (+23.67%)
- **Active Bot Investments:** 3

### 🤖 Active Bot Investments

#### 1. MoonShiba Pro
- **Investment:** $2,500.00
- **Current Value:** $3,112.50
- **Profit/Loss:** +$612.50 (+24.5%)
- **Strategy:** Momentum Trading
- **Target Coins:** SHIB, DOGE, PEPE, FLOKI

#### 2. Arbitrage Master
- **Investment:** $2,000.00
- **Current Value:** $2,364.00
- **Profit/Loss:** +$364.00 (+18.2%)
- **Strategy:** Cross-Exchange Arbitrage
- **Target Coins:** DOGE, SHIB, FLOKI, BONK

#### 3. DCA Diamond Hands
- **Investment:** $1,500.00
- **Current Value:** $1,944.00
- **Profit/Loss:** +$444.00 (+29.6%)
- **Strategy:** Dollar-Cost Averaging
- **Target Coins:** PEPE, WIF, BONK, MEME

### 💳 Wallet Balances
- **USD:** $1,200.00
- **ETH:** 0.8542 ETH
- **BTC:** 0.0234 BTC

### 📊 Recent Trading Activity
- **Total Trades:** 156 (across all bots)
- **Win Rate:** 68.2% average
- **Last Trade:** January 15, 2024 at 4:30 PM
- **Most Profitable Trade:** +$1.84 on SHIB

## 🌐 Application Routes

### 📱 Main Pages
- **Landing Page:** `/` - Marketing homepage with featured bots
- **Authentication:** `/login` or `/signup` - Unified auth with smooth transitions
- **Dashboard:** `/dashboard` - Portfolio overview and quick actions
- **Trading Bots:** `/bots` - Browse and filter available bots
- **Bot Details:** `/bots/detail` - Detailed bot information and investment
- **Portfolio:** `/portfolio` - Track investments and performance
- **Wallet:** `/wallet` - Manage crypto funds and transactions
- **Settings:** `/settings` - User preferences and account management

### 🔄 User Flow
1. **Landing Page** → View featured bots and platform benefits
2. **Authentication** → Unified login/signup with smooth animations
3. **Dashboard** → Overview of portfolio and active investments
4. **Browse Bots** → Explore available trading bots
5. **Invest** → Choose bot and investment amount
6. **Monitor** → Track performance in portfolio
7. **Manage** → Deposit/withdraw funds in wallet

## 🎨 UI/UX Features

### ✨ Design Elements
- **Color Scheme:** Purple/Pink gradients with dark theme
- **Icons:** FontAwesome icon system
- **Animations:** Smooth transitions and hover effects
- **Glass Effects:** Modern glassmorphism design
- **Responsive:** Mobile-first responsive design

### ♿ Accessibility Features
- **ARIA Labels:** Proper accessibility labels
- **Keyboard Navigation:** Full keyboard support
- **Screen Reader:** Compatible with screen readers
- **Color Contrast:** High contrast for readability
- **Focus Indicators:** Clear focus states

### 🔧 Technical Features
- **TypeScript:** Full type safety
- **React Router:** Client-side routing
- **Mock Data:** Realistic demo data
- **Error Handling:** Comprehensive error management
- **Loading States:** User-friendly loading indicators

## 🚀 Getting Started

1. **Visit the application:** http://localhost:5173/
2. **Explore the landing page** to see featured bots
3. **Click "Get Started"** to go to signup page
4. **Or click "Sign In"** to use demo credentials
5. **Login with demo account:**
   - Username: `demo`
   - Password: `demo123`
6. **Explore the dashboard** and all features

## 📝 Notes

- All data is **mock/demo data** for demonstration purposes
- **No real trading** or financial transactions occur
- **Wallet connections** are simulated (Web3 integration ready)
- **Perfect for demos, testing, and development**

---

**🎯 Ready for production with real API integrations, Web3 wallet connections, and live trading functionality!**
