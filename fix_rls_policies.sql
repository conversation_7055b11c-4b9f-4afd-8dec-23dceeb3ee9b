-- ==============================================
-- QUICK FIX FOR RLS POLICIES
-- ==============================================
-- Run this SQL in your Supabase SQL Editor to fix the authentication issues

-- Add INSERT policy for users table
CREATE POLICY "Users can insert own data" ON users FOR INSERT WITH CHECK (auth.uid() = id);

-- Add INSERT policy for portfolios table  
CREATE POLICY "Users can create own portfolio" ON portfolios FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Make trading_bots readable by all authenticated users
ALTER TABLE trading_bots ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Authenticated users can view trading bots" ON trading_bots FOR SELECT TO authenticated USING (is_active = true);

-- Allow admins to manage trading bots (optional, for admin functionality)
CREATE POLICY "Admins can manage trading bots" ON trading_bots FOR ALL TO authenticated USING (
  EXISTS (
    SELECT 1 FROM admin_users 
    WHERE admin_users.id = auth.uid() 
    AND admin_users.is_active = true
  )
);

-- Add policy for referrals creation
CREATE POLICY "Users can create referrals" ON referrals FOR INSERT WITH CHECK (auth.uid() = referrer_id);
