# MemeBot Pro - Admin Dashboard

A standalone administrative interface for managing the MemeBot Pro crypto trading platform. This admin panel can be hosted separately from the main application for enhanced security and scalability.

## Features

- 🔐 **Secure Authentication** - Role-based access control with Supabase
- 📊 **Dashboard Overview** - Real-time statistics and system monitoring
- 👥 **User Management** - Manage users, KYC status, and verification
- 🤖 **Trading Bot Management** - Configure and monitor trading bots
- 💰 **Transaction Monitoring** - Track all financial transactions
- 📈 **Analytics** - Comprehensive platform analytics
- 🔍 **Audit Logs** - Complete audit trail of admin actions
- ⚙️ **System Settings** - Platform configuration management

## Technology Stack

- **Frontend**: React 18 + TypeScript
- **Styling**: Tailwind CSS
- **Icons**: FontAwesome
- **Database**: Supabase (PostgreSQL)
- **Build Tool**: Vite
- **Authentication**: Supabase Auth with RLS

## Quick Start

### Prerequisites

- Node.js 18+ or Bun
- Supabase account and project
- Environment variables configured

### Installation

1. **Clone and navigate to admin directory**:
   ```bash
   cd admin
   ```

2. **Install dependencies**:
   ```bash
   # Using npm
   npm install
   
   # Using bun
   bun install
   ```

3. **Environment Setup**:
   Create a `.env` file in the admin directory:
   ```env
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. **Database Setup**:
   Run the migration script in your Supabase SQL editor:
   ```bash
   # The migration file is located at:
   ../database/migrations/001_initial_schema.sql
   ```

5. **Start Development Server**:
   ```bash
   # Using npm
   npm run dev
   
   # Using bun
   bun run dev
   ```

   The admin panel will be available at `http://localhost:3001`

### Production Build

```bash
# Build for production
npm run build

# Preview production build
npm run preview
```

## Project Structure

```
admin/
├── src/
│   ├── main.tsx          # Application entry point
│   └── index.css         # Global styles
├── lib/
│   ├── supabase.ts       # Supabase client and database operations
│   └── AuthContext.tsx   # Authentication context and hooks
├── components/
│   └── Layout.tsx        # Main layout component
├── pages/
│   ├── Login.tsx         # Admin login page
│   ├── Dashboard.tsx     # Main dashboard
│   └── UserManagement.tsx # User management interface
├── App.tsx               # Main app component
├── package.json          # Dependencies and scripts
├── vite.config.ts        # Vite configuration
├── tailwind.config.js    # Tailwind CSS configuration
└── tsconfig.json         # TypeScript configuration
```

## Authentication & Security

### Admin User Setup

1. **Create Admin User in Database**:
   ```sql
   INSERT INTO admin_users (email, username, name, role, permissions) 
   VALUES ('<EMAIL>', 'admin', 'System Administrator', 'super_admin', '{"all": true}');
   ```

2. **Create Supabase Auth User**:
   - Go to Supabase Dashboard > Authentication > Users
   - Create a new user with the same email
   - Set a secure password

### Role-Based Access Control

- **super_admin**: Full access to all features
- **admin**: Standard administrative access
- **moderator**: Limited access to user management

### Security Features

- Row Level Security (RLS) enabled on all tables
- Admin-only access verification
- Audit logging for all admin actions
- Secure session management
- CSRF protection

## Deployment Options

### Option 1: Separate Hosting (Recommended)

Deploy the admin panel on a separate domain/subdomain:

```bash
# Build the admin panel
npm run build

# Deploy to your hosting provider
# Examples: Vercel, Netlify, AWS S3 + CloudFront
```

**Benefits**:
- Enhanced security isolation
- Independent scaling
- Separate SSL certificates
- Different access controls

### Option 2: Subdirectory Hosting

Host admin panel under `/admin` path of main domain:

```bash
# Configure base path in vite.config.ts
export default defineConfig({
  base: '/admin/',
  // ... other config
})
```

### Environment Variables for Production

```env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key
```

## Database Schema

The admin panel uses the following main tables:

- `admin_users` - Admin user accounts and permissions
- `users` - Platform users
- `trading_bots` - Trading bot configurations
- `portfolios` - User portfolio data
- `trades` - Trading history
- `wallet_transactions` - Financial transactions
- `audit_logs` - Admin action logs
- `system_settings` - Platform configuration

## API Reference

### Admin Authentication

```typescript
// Sign in admin user
const { data, error } = await adminSignIn(email, password);

// Get current admin session
const { session, adminUser } = await getAdminSession();

// Sign out
await adminSignOut();
```

### User Management

```typescript
// Get users with pagination
const { data, count } = await getUsers(page, limit, search);

// Update user status
await updateUserStatus(userId, { kyc_status: 'approved' });
```

### Dashboard Statistics

```typescript
// Get dashboard stats
const { data } = await getDashboardStats();
```

## Contributing

1. Follow TypeScript best practices
2. Use Tailwind CSS for styling
3. Implement proper error handling
4. Add audit logging for admin actions
5. Test with different admin roles

## Security Considerations

- Always use HTTPS in production
- Implement proper CORS policies
- Regular security audits
- Monitor admin access logs
- Use strong authentication policies
- Keep dependencies updated

## Support

For issues and questions:
- Check the main application documentation
- Review Supabase documentation
- Contact the development team

## License

MIT License - see main project for details
