/**
 * ==============================================
 * ADMIN DASHBOARD STYLES
 * ==============================================
 * 
 * Tailwind CSS and custom styles for admin interface
 */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar for dark theme */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1f2937;
}

::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Custom focus styles */
.focus-ring:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Table hover effects */
.table-row-hover:hover {
  background-color: rgba(55, 65, 81, 0.5);
  transition: background-color 0.2s ease;
}

/* Status indicators */
.status-online {
  @apply bg-green-500;
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
}

.status-offline {
  @apply bg-red-500;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.status-warning {
  @apply bg-yellow-500;
  box-shadow: 0 0 0 2px rgba(234, 179, 8, 0.2);
}

/* Custom button styles */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-secondary {
  @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-success {
  @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

/* Card styles */
.card {
  @apply bg-gray-800 border border-gray-700 rounded-lg shadow-lg;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-700;
}

.card-body {
  @apply p-6;
}

/* Form styles */
.form-input {
  @apply w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors duration-200;
}

.form-label {
  @apply block text-sm font-medium text-gray-300 mb-2;
}

/* Loading spinner */
.spinner {
  @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-gray-800 {
    background-color: #000000;
  }
  
  .text-gray-400 {
    color: #ffffff;
  }
  
  .border-gray-700 {
    border-color: #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
