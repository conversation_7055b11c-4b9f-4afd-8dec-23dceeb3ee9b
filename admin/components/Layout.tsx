/**
 * ==============================================
 * ADMIN LAYOUT COMPONENT
 * ==============================================
 * 
 * Main layout wrapper for admin dashboard with navigation
 */

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faHome, 
  faUsers, 
  faRobot, 
  faChartLine, 
  faWallet, 
  faCog, 
  faSignOutAlt, 
  faBars, 
  faTimes,
  faShield,
  faFileAlt,
  faBell
} from '@fortawesome/free-solid-svg-icons';
import { useAdminAuth } from '../lib/AuthContext';

interface AdminLayoutProps {
  children: React.ReactNode;
  currentPage: string;
  onNavigate: (page: string) => void;
}

export function AdminLayout({ children, currentPage, onNavigate }: AdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { adminUser, signOut } = useAdminAuth();

  const navigation = [
    { name: 'Dashboard', href: 'dashboard', icon: faHome },
    { name: 'Users', href: 'users', icon: faUsers },
    { name: 'Trading Bots', href: 'bots', icon: faRobot },
    { name: 'Transactions', href: 'transactions', icon: faWallet },
    { name: 'Analytics', href: 'analytics', icon: faChartLine },
    { name: 'Audit Logs', href: 'logs', icon: faFileAlt },
    { name: 'Settings', href: 'settings', icon: faCog },
  ];

  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        >
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75"></div>
        </div>
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-gray-800 transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="flex items-center justify-between h-16 px-4 bg-gray-900">
          <div className="flex items-center space-x-2">
            <FontAwesomeIcon icon={faShield} className="h-8 w-8 text-blue-400" />
            <span className="text-xl font-bold text-white">Admin Portal</span>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden text-gray-400 hover:text-white"
          >
            <FontAwesomeIcon icon={faTimes} className="h-6 w-6" />
          </button>
        </div>

        {/* Navigation */}
        <nav className="mt-8 px-4">
          <div className="space-y-2">
            {navigation.map((item) => (
              <button
                key={item.name}
                onClick={() => {
                  onNavigate(item.href);
                  setSidebarOpen(false);
                }}
                className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
                  currentPage === item.href
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <FontAwesomeIcon icon={item.icon} className="mr-3 h-5 w-5" />
                {item.name}
              </button>
            ))}
          </div>
        </nav>

        {/* User info and logout */}
        <div className="absolute bottom-0 left-0 right-0 p-4 bg-gray-900">
          <div className="flex items-center space-x-3 mb-4">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 rounded-full bg-blue-600 flex items-center justify-center">
                <FontAwesomeIcon icon={faUsers} className="h-5 w-5 text-white" />
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-white truncate">
                {adminUser?.name}
              </p>
              <p className="text-xs text-gray-400 truncate">
                {adminUser?.role.replace('_', ' ').toUpperCase()}
              </p>
            </div>
          </div>
          <button
            onClick={handleSignOut}
            className="w-full flex items-center px-4 py-2 text-sm font-medium text-gray-300 hover:bg-gray-700 hover:text-white rounded-lg transition-colors"
          >
            <FontAwesomeIcon icon={faSignOutAlt} className="mr-3 h-5 w-5" />
            Sign Out
          </button>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-10 bg-gray-800 border-b border-gray-700">
          <div className="flex items-center justify-between h-16 px-4">
            <div className="flex items-center">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden text-gray-400 hover:text-white mr-4"
              >
                <FontAwesomeIcon icon={faBars} className="h-6 w-6" />
              </button>
              <h1 className="text-xl font-semibold text-white capitalize">
                {currentPage === 'dashboard' ? 'Dashboard' : currentPage}
              </h1>
            </div>

            <div className="flex items-center space-x-4">
              {/* Notifications */}
              <button className="text-gray-400 hover:text-white relative">
                <FontAwesomeIcon icon={faBell} className="h-6 w-6" />
                <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"></span>
              </button>

              {/* User menu */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-300">{adminUser?.name}</span>
                <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                  <FontAwesomeIcon icon={faUsers} className="h-4 w-4 text-white" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
