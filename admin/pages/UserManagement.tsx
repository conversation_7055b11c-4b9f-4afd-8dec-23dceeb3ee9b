/**
 * ==============================================
 * ADMIN USER MANAGEMENT PAGE
 * ==============================================
 * 
 * Interface for managing users, KYC status, and user data
 */

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faUsers, 
  faSearch, 
  faFilter, 
  faEdit, 
  faTrash, 
  faCheck, 
  faTimes, 
  faSpinner,
  faEye,
  faShield,
  faWallet
} from '@fortawesome/free-solid-svg-icons';
import { getUsers, updateUserStatus } from '../lib/supabase';

interface User {
  id: string;
  email: string;
  username: string;
  name: string;
  is_verified: boolean;
  kyc_status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  portfolios: any[];
  bot_investments: any[];
}

export function UserManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalUsers, setTotalUsers] = useState(0);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showUserModal, setShowUserModal] = useState(false);

  const usersPerPage = 20;

  useEffect(() => {
    loadUsers();
  }, [currentPage, searchTerm]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const { data, error, count } = await getUsers(currentPage, usersPerPage, searchTerm);
      
      if (error) {
        setError('Failed to load users');
        console.error('Users error:', error);
      } else {
        setUsers(data || []);
        setTotalUsers(count || 0);
      }
    } catch (err) {
      setError('Failed to load users');
      console.error('Users error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleKYCStatusChange = async (userId: string, newStatus: string) => {
    try {
      const { data, error } = await updateUserStatus(userId, { kyc_status: newStatus });
      
      if (error) {
        alert('Failed to update KYC status');
      } else {
        // Update local state
        setUsers(users.map(user => 
          user.id === userId ? { ...user, kyc_status: newStatus as any } : user
        ));
      }
    } catch (err) {
      alert('Failed to update KYC status');
    }
  };

  const handleVerificationToggle = async (userId: string, isVerified: boolean) => {
    try {
      const { data, error } = await updateUserStatus(userId, { is_verified: !isVerified });
      
      if (error) {
        alert('Failed to update verification status');
      } else {
        // Update local state
        setUsers(users.map(user => 
          user.id === userId ? { ...user, is_verified: !isVerified } : user
        ));
      }
    } catch (err) {
      alert('Failed to update verification status');
    }
  };

  const getKYCStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-400 bg-green-400/20';
      case 'rejected': return 'text-red-400 bg-red-400/20';
      default: return 'text-yellow-400 bg-yellow-400/20';
    }
  };

  const totalPages = Math.ceil(totalUsers / usersPerPage);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">User Management</h1>
          <p className="text-gray-400 mt-1">Manage users, KYC status, and verification</p>
        </div>
        <div className="text-sm text-gray-400">
          Total Users: {totalUsers}
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <FontAwesomeIcon 
              icon={faSearch} 
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" 
            />
            <input
              type="text"
              placeholder="Search users by name, email, or username..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
            />
          </div>
          <button
            onClick={loadUsers}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            <FontAwesomeIcon icon={faFilter} className="mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <FontAwesomeIcon icon={faSpinner} className="h-8 w-8 text-blue-500 animate-spin" />
          </div>
        ) : error ? (
          <div className="p-6 text-center">
            <p className="text-red-400">{error}</p>
            <button 
              onClick={loadUsers}
              className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
            >
              Retry
            </button>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      KYC Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Verified
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Portfolio
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Joined
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-700">
                  {users.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-700/50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-white">{user.name}</div>
                          <div className="text-sm text-gray-400">{user.email}</div>
                          <div className="text-xs text-gray-500">@{user.username}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <select
                          value={user.kyc_status}
                          onChange={(e) => handleKYCStatusChange(user.id, e.target.value)}
                          className={`px-2 py-1 rounded-full text-xs font-medium border-0 ${getKYCStatusColor(user.kyc_status)}`}
                        >
                          <option value="pending">Pending</option>
                          <option value="approved">Approved</option>
                          <option value="rejected">Rejected</option>
                        </select>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <button
                          onClick={() => handleVerificationToggle(user.id, user.is_verified)}
                          className={`p-1 rounded ${user.is_verified ? 'text-green-400' : 'text-gray-400'}`}
                        >
                          <FontAwesomeIcon icon={user.is_verified ? faCheck : faTimes} />
                        </button>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-white">
                          ${user.portfolios?.[0]?.total_balance || '0.00'}
                        </div>
                        <div className="text-xs text-gray-400">
                          {user.bot_investments?.length || 0} investments
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                        {new Date(user.created_at).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => {
                              setSelectedUser(user);
                              setShowUserModal(true);
                            }}
                            className="text-blue-400 hover:text-blue-300"
                          >
                            <FontAwesomeIcon icon={faEye} />
                          </button>
                          <button className="text-yellow-400 hover:text-yellow-300">
                            <FontAwesomeIcon icon={faEdit} />
                          </button>
                          <button className="text-red-400 hover:text-red-300">
                            <FontAwesomeIcon icon={faTrash} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="bg-gray-700 px-6 py-3 flex items-center justify-between">
              <div className="text-sm text-gray-400">
                Showing {((currentPage - 1) * usersPerPage) + 1} to {Math.min(currentPage * usersPerPage, totalUsers)} of {totalUsers} users
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 bg-gray-600 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-500"
                >
                  Previous
                </button>
                <span className="px-3 py-1 text-gray-400">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 bg-gray-600 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-500"
                >
                  Next
                </button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
