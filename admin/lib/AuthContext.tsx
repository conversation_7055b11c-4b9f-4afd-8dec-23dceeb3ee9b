/**
 * ==============================================
 * ADMIN AUTHENTICATION CONTEXT
 * ==============================================
 * 
 * Manages admin authentication state and provides auth methods
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User } from '@supabase/supabase-js';
import { adminSupabase, getAdminSession, adminSignOut } from './supabase';

interface AdminUser {
  id: string;
  email: string;
  username: string;
  name: string;
  role: 'super_admin' | 'admin' | 'moderator';
  permissions: any;
  is_active: boolean;
  last_login: string | null;
  created_at: string;
  updated_at: string;
}

interface AdminAuthContextType {
  user: User | null;
  adminUser: AdminUser | null;
  loading: boolean;
  signOut: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
  isRole: (role: string) => boolean;
}

const AdminAuthContext = createContext<AdminAuthContextType | undefined>(undefined);

export function AdminAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { session, adminUser: admin, error } = await getAdminSession();
        if (error) {
          console.error('Error getting admin session:', error);
        } else {
          setUser(session?.user ?? null);
          setAdminUser(admin);
        }
      } catch (error) {
        console.error('Error getting initial admin session:', error);
      } finally {
        setLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = adminSupabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Admin auth state changed:', event, session?.user?.id);
        
        if (session?.user) {
          // Verify admin status
          const { adminUser: admin, error } = await getAdminSession();
          if (error || !admin) {
            setUser(null);
            setAdminUser(null);
          } else {
            setUser(session.user);
            setAdminUser(admin);
          }
        } else {
          setUser(null);
          setAdminUser(null);
        }
        setLoading(false);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signOut = async () => {
    try {
      const { error } = await adminSignOut();
      if (error) {
        console.error('Error signing out:', error);
      }
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!adminUser) return false;
    
    // Super admin has all permissions
    if (adminUser.role === 'super_admin') return true;
    
    // Check specific permissions
    if (adminUser.permissions?.all) return true;
    if (adminUser.permissions?.[permission]) return true;
    
    return false;
  };

  const isRole = (role: string): boolean => {
    return adminUser?.role === role;
  };

  const value = {
    user,
    adminUser,
    loading,
    signOut,
    hasPermission,
    isRole,
  };

  return (
    <AdminAuthContext.Provider value={value}>
      {children}
    </AdminAuthContext.Provider>
  );
}

export function useAdminAuth() {
  const context = useContext(AdminAuthContext);
  if (context === undefined) {
    throw new Error('useAdminAuth must be used within an AdminAuthProvider');
  }
  return context;
}

/**
 * Higher-order component for protecting admin routes
 */
export function withAdminAuth<P extends object>(
  Component: React.ComponentType<P>,
  requiredPermission?: string
) {
  return function ProtectedComponent(props: P) {
    const { adminUser, loading, hasPermission } = useAdminAuth();

    if (loading) {
      return (
        <div className="flex items-center justify-center min-h-screen bg-gray-900">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      );
    }

    if (!adminUser) {
      return (
        <div className="flex items-center justify-center min-h-screen bg-gray-900">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-white mb-4">Access Denied</h2>
            <p className="text-gray-400">Admin authentication required</p>
          </div>
        </div>
      );
    }

    if (requiredPermission && !hasPermission(requiredPermission)) {
      return (
        <div className="flex items-center justify-center min-h-screen bg-gray-900">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-white mb-4">Insufficient Permissions</h2>
            <p className="text-gray-400">You don't have permission to access this resource</p>
          </div>
        </div>
      );
    }

    return <Component {...props} />;
  };
}
