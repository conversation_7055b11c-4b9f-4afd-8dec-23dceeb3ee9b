/**
 * ==============================================
 * ADMIN SUPABASE CLIENT CONFIGURATION
 * ==============================================
 * 
 * Separate Supabase client configuration for admin panel
 * This allows the admin panel to be hosted separately if needed
 */

import { createClient } from '@supabase/supabase-js';

// ==============================================
// SUPABASE CONFIGURATION
// ==============================================

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables for admin panel');
}

// Create Supabase client for admin operations
export const adminSupabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

// ==============================================
// ADMIN DATABASE OPERATIONS
// ==============================================

/**
 * Admin authentication
 */
export async function adminSignIn(email: string, password: string) {
  try {
    // First authenticate with Supabase
    const { data: authData, error: authError } = await adminSupabase.auth.signInWithPassword({
      email,
      password
    });

    if (authError) {
      return { data: null, error: authError.message };
    }

    // Check if user exists in admin_users table
    const { data: adminUser, error: adminError } = await adminSupabase
      .from('admin_users')
      .select('*')
      .eq('email', email)
      .eq('is_active', true)
      .single();

    if (adminError || !adminUser) {
      // Sign out if not an admin user
      await adminSupabase.auth.signOut();
      return { data: null, error: 'Access denied. Admin privileges required.' };
    }

    // Update last login
    await adminSupabase
      .from('admin_users')
      .update({ last_login: new Date().toISOString() })
      .eq('id', adminUser.id);

    return { 
      data: { 
        session: authData.session, 
        user: authData.user,
        adminUser 
      }, 
      error: null 
    };
  } catch (error) {
    console.error('Admin signin error:', error);
    return { data: null, error: 'Authentication failed' };
  }
}

/**
 * Admin sign out
 */
export async function adminSignOut() {
  try {
    const { error } = await adminSupabase.auth.signOut();
    return { error };
  } catch (error) {
    console.error('Admin signout error:', error);
    return { error };
  }
}

/**
 * Get current admin session
 */
export async function getAdminSession() {
  try {
    const { data: { session }, error } = await adminSupabase.auth.getSession();
    
    if (error || !session) {
      return { session: null, adminUser: null, error };
    }

    // Get admin user data
    const { data: adminUser, error: adminError } = await adminSupabase
      .from('admin_users')
      .select('*')
      .eq('email', session.user.email)
      .eq('is_active', true)
      .single();

    if (adminError || !adminUser) {
      await adminSupabase.auth.signOut();
      return { session: null, adminUser: null, error: 'Admin access revoked' };
    }

    return { session, adminUser, error: null };
  } catch (error) {
    console.error('Get admin session error:', error);
    return { session: null, adminUser: null, error };
  }
}

/**
 * Get dashboard statistics
 */
export async function getDashboardStats() {
  try {
    const [
      { count: totalUsers },
      { count: totalBots },
      { count: activeTrades },
      { count: totalTransactions }
    ] = await Promise.all([
      adminSupabase.from('users').select('*', { count: 'exact', head: true }),
      adminSupabase.from('trading_bots').select('*', { count: 'exact', head: true }),
      adminSupabase.from('trades').select('*', { count: 'exact', head: true }).eq('status', 'completed'),
      adminSupabase.from('wallet_transactions').select('*', { count: 'exact', head: true })
    ]);

    // Get total platform value
    const { data: portfolios } = await adminSupabase
      .from('portfolios')
      .select('total_balance');

    const totalValue = portfolios?.reduce((sum, p) => sum + (parseFloat(p.total_balance) || 0), 0) || 0;

    // Get recent activity
    const { data: recentTrades } = await adminSupabase
      .from('trades')
      .select(`
        *,
        users(name, email),
        trading_bots(name)
      `)
      .order('created_at', { ascending: false })
      .limit(10);

    return {
      data: {
        totalUsers: totalUsers || 0,
        totalBots: totalBots || 0,
        activeTrades: activeTrades || 0,
        totalTransactions: totalTransactions || 0,
        totalValue,
        recentTrades: recentTrades || []
      },
      error: null
    };
  } catch (error) {
    console.error('Dashboard stats error:', error);
    return { data: null, error };
  }
}

/**
 * Get all users with pagination
 */
export async function getUsers(page = 1, limit = 20, search = '') {
  try {
    let query = adminSupabase
      .from('users')
      .select(`
        *,
        portfolios(total_balance, total_profit_loss),
        bot_investments(amount, profit_loss)
      `, { count: 'exact' });

    if (search) {
      query = query.or(`name.ilike.%${search}%,email.ilike.%${search}%,username.ilike.%${search}%`);
    }

    const { data, error, count } = await query
      .order('created_at', { ascending: false })
      .range((page - 1) * limit, page * limit - 1);

    return { data, error, count };
  } catch (error) {
    console.error('Get users error:', error);
    return { data: null, error, count: 0 };
  }
}

/**
 * Get all trading bots
 */
export async function getTradingBots() {
  try {
    const { data, error } = await adminSupabase
      .from('trading_bots')
      .select(`
        *,
        admin_users(name),
        bot_investments(amount, status)
      `)
      .order('created_at', { ascending: false });

    return { data, error };
  } catch (error) {
    console.error('Get trading bots error:', error);
    return { data: null, error };
  }
}

/**
 * Update user status
 */
export async function updateUserStatus(userId: string, updates: any) {
  try {
    const { data, error } = await adminSupabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single();

    return { data, error };
  } catch (error) {
    console.error('Update user status error:', error);
    return { data: null, error };
  }
}

/**
 * Create audit log
 */
export async function createAuditLog(adminId: string, action: string, details: any) {
  try {
    const { data, error } = await adminSupabase
      .from('audit_logs')
      .insert({
        admin_id: adminId,
        action,
        ...details
      });

    return { data, error };
  } catch (error) {
    console.error('Create audit log error:', error);
    return { data: null, error };
  }
}
