#!/bin/bash

# ==============================================
# ADMIN PANEL DEPLOYMENT SCRIPT
# ==============================================
# 
# Script to build and deploy the admin panel
# Can be customized for different hosting providers

set -e

echo "🚀 Starting Admin Panel Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BUILD_DIR="dist"
BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the admin directory
if [ ! -f "package.json" ]; then
    print_error "Please run this script from the admin directory"
    exit 1
fi

# Check for required environment variables
if [ ! -f ".env" ]; then
    print_warning "No .env file found. Make sure environment variables are set."
fi

# Install dependencies
print_status "Installing dependencies..."
if command -v bun &> /dev/null; then
    bun install
else
    npm install
fi

# Run type checking
print_status "Running type checks..."
if command -v bun &> /dev/null; then
    bun run type-check
else
    npm run type-check
fi

# Run linting
print_status "Running linter..."
if command -v bun &> /dev/null; then
    bun run lint
else
    npm run lint
fi

# Create backup of existing build
if [ -d "$BUILD_DIR" ]; then
    print_status "Creating backup of existing build..."
    mv "$BUILD_DIR" "$BACKUP_DIR"
fi

# Build the application
print_status "Building admin panel..."
if command -v bun &> /dev/null; then
    bun run build
else
    npm run build
fi

# Verify build was successful
if [ ! -d "$BUILD_DIR" ]; then
    print_error "Build failed - no dist directory found"
    
    # Restore backup if it exists
    if [ -d "$BACKUP_DIR" ]; then
        print_status "Restoring backup..."
        mv "$BACKUP_DIR" "$BUILD_DIR"
    fi
    
    exit 1
fi

print_success "Build completed successfully!"

# Optional: Deploy to hosting provider
# Uncomment and customize based on your hosting provider

# Example: Deploy to Vercel
# print_status "Deploying to Vercel..."
# vercel --prod

# Example: Deploy to Netlify
# print_status "Deploying to Netlify..."
# netlify deploy --prod --dir=dist

# Example: Deploy to AWS S3
# print_status "Deploying to AWS S3..."
# aws s3 sync dist/ s3://your-admin-bucket --delete

# Example: Deploy via SCP
# print_status "Deploying via SCP..."
# scp -r dist/* user@server:/path/to/admin/

# Example: Deploy via rsync
# print_status "Deploying via rsync..."
# rsync -avz --delete dist/ user@server:/path/to/admin/

# Clean up old backup
if [ -d "$BACKUP_DIR" ]; then
    print_status "Cleaning up backup..."
    rm -rf "$BACKUP_DIR"
fi

print_success "Admin panel deployment completed!"
print_status "Build output is in the '$BUILD_DIR' directory"

# Optional: Open preview
# print_status "Starting preview server..."
# if command -v bun &> /dev/null; then
#     bun run preview
# else
#     npm run preview
# fi
