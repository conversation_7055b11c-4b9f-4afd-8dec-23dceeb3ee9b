/**
 * ==============================================
 * ADMIN APPLICATION MAIN COMPONENT
 * ==============================================
 * 
 * Main admin application with routing and authentication
 * This can be hosted separately from the main application
 */

import React, { useState } from 'react';
import { AdminAuthProvider, useAdminAuth } from './lib/AuthContext';
import { AdminLogin } from './pages/Login';
import { AdminLayout } from './components/Layout';
import { AdminDashboard } from './pages/Dashboard';
import { UserManagement } from './pages/UserManagement';

// Import FontAwesome library setup
import { library } from '@fortawesome/fontawesome-svg-core';
import { 
  faHome, 
  faUsers, 
  faRobot, 
  faChartLine, 
  faWallet, 
  faCog, 
  faSignOutAlt, 
  faBars, 
  faTimes,
  faShield,
  faFileAlt,
  faBell,
  faUser,
  faLock,
  faEye,
  faEyeSlash,
  faSpinner,
  faSearch,
  faFilter,
  faEdit,
  faTrash,
  faCheck,
  faArrowUp,
  faArrowDown,
  faDollarSign
} from '@fortawesome/free-solid-svg-icons';

// Add icons to library
library.add(
  faHome, 
  faUsers, 
  faRobot, 
  faChartLine, 
  faWallet, 
  faCog, 
  faSignOutAlt, 
  faBars, 
  faTimes,
  faShield,
  faFileAlt,
  faBell,
  faUser,
  faLock,
  faEye,
  faEyeSlash,
  faSpinner,
  faSearch,
  faFilter,
  faEdit,
  faTrash,
  faCheck,
  faArrowUp,
  faArrowDown,
  faDollarSign
);

function AdminAppContent() {
  const { adminUser, loading } = useAdminAuth();
  const [currentPage, setCurrentPage] = useState('dashboard');

  const handleLoginSuccess = () => {
    setCurrentPage('dashboard');
  };

  const handleNavigate = (page: string) => {
    setCurrentPage(page);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!adminUser) {
    return <AdminLogin onLoginSuccess={handleLoginSuccess} />;
  }

  const renderPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return <AdminDashboard />;
      case 'users':
        return <UserManagement />;
      case 'bots':
        return (
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-white mb-4">Trading Bot Management</h2>
            <p className="text-gray-400">Coming soon...</p>
          </div>
        );
      case 'transactions':
        return (
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-white mb-4">Transaction Management</h2>
            <p className="text-gray-400">Coming soon...</p>
          </div>
        );
      case 'analytics':
        return (
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-white mb-4">Analytics Dashboard</h2>
            <p className="text-gray-400">Coming soon...</p>
          </div>
        );
      case 'logs':
        return (
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-white mb-4">Audit Logs</h2>
            <p className="text-gray-400">Coming soon...</p>
          </div>
        );
      case 'settings':
        return (
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-white mb-4">System Settings</h2>
            <p className="text-gray-400">Coming soon...</p>
          </div>
        );
      default:
        return <AdminDashboard />;
    }
  };

  return (
    <AdminLayout currentPage={currentPage} onNavigate={handleNavigate}>
      {renderPage()}
    </AdminLayout>
  );
}

export function AdminApp() {
  return (
    <AdminAuthProvider>
      <AdminAppContent />
    </AdminAuthProvider>
  );
}

export default AdminApp;
